part of 'theme_bloc.dart';

abstract class ThemeState extends Equatable {
  final ThemeData themeData;
  final bool isDarkMode;

  const ThemeState(this.themeData, this.isDarkMode);
  @override
  List<Object?> get props => [themeData, isDarkMode];
}

// Helper function to create MaterialColor from Color
MaterialColor _createMaterialColor(Color color) {
  List strengths = <double>[.05];
  Map<int, Color> swatch = <int, Color>{};
  final int r = (color.r * 255.0).round() & 0xff;
  final int g = (color.g * 255.0).round() & 0xff;
  final int b = (color.b * 255.0).round() & 0xff;

  for (int i = 1; i < 10; i++) {
    strengths.add(0.1 * i);
  }
  for (var strength in strengths) {
    final double ds = 0.5 - strength;
    swatch[(strength * 1000).round()] = Color.fromRGBO(
      r + ((ds < 0 ? r : (255 - r)) * ds).round(),
      g + ((ds < 0 ? g : (255 - g)) * ds).round(),
      b + ((ds < 0 ? b : (255 - b)) * ds).round(),
      1,
    );
  }
  return MaterialColor(color.toARGB32(), swatch);
}

class LightThemeState extends ThemeState {
  LightThemeState() : super(_lightTheme, false);

  static final ThemeData _lightTheme = ThemeData(
    useMaterial3: false,
    brightness: Brightness.light,
    primarySwatch:
        _createMaterialColor(HexColor.fromHex(APK_DETAILS['theme_color']!)),
    primaryColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: AppBarTheme(
      backgroundColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
      foregroundColor: HexColor.fromHex(APK_DETAILS['theme_forground_color']!),
      elevation: 0,
      iconTheme: IconThemeData(
        color: HexColor.fromHex(APK_DETAILS['theme_forground_color']!),
      ),
      titleTextStyle: TextStyle(
        color: HexColor.fromHex(APK_DETAILS['theme_forground_color']!),
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    cardTheme: CardThemeData(
      color: Colors.white,
      elevation: 2,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
        foregroundColor:
            HexColor.fromHex(APK_DETAILS['theme_forground_color']!),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
        side: BorderSide(color: HexColor.fromHex(APK_DETAILS['theme_color']!)),
      ),
    ),
    textTheme: const TextTheme(
      displayLarge: TextStyle(color: ColorConstants.headingTitle),
      displayMedium: TextStyle(color: ColorConstants.headingTitle),
      displaySmall: TextStyle(color: ColorConstants.headingTitle),
      headlineLarge: TextStyle(color: ColorConstants.headingTitle),
      headlineMedium: TextStyle(color: ColorConstants.headingTitle),
      headlineSmall: TextStyle(color: ColorConstants.headingTitle),
      titleLarge: TextStyle(
          color: ColorConstants.headingTitle, fontWeight: FontWeight.w600),
      titleMedium: TextStyle(
          color: ColorConstants.subHeadingTitle, fontWeight: FontWeight.w500),
      titleSmall: TextStyle(
          color: ColorConstants.subHeadingTitle, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(color: ColorConstants.bodyText),
      bodyMedium: TextStyle(color: ColorConstants.bodyText),
      bodySmall: TextStyle(color: ColorConstants.bodyText),
      labelLarge: TextStyle(color: ColorConstants.bodyText),
      labelMedium: TextStyle(color: ColorConstants.bodyText),
      labelSmall: TextStyle(color: ColorConstants.lebelText),
    ),
    iconTheme: const IconThemeData(color: ColorConstants.bodyText),
    dividerTheme: const DividerThemeData(
      color: ColorConstants.dividerColor1,
      thickness: 1,
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return HexColor.fromHex(APK_DETAILS['theme_color']!);
        }
        return Colors.grey;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return HexColor.fromHex(APK_DETAILS['theme_color']!)
              .withValues(alpha: 0.5);
        }
        return Colors.grey.withValues(alpha: 0.3);
      }),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
      unselectedItemColor:
          const Color(0xFFBFBFBF), // ColorConstants.inactiveTab
      type: BottomNavigationBarType.fixed,
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xffF2F2F2), // ColorConstants.textFieldBg
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide:
            BorderSide(color: HexColor.fromHex(APK_DETAILS['theme_color']!)),
      ),
    ),
  );
}

class DarkThemeState extends ThemeState {
  DarkThemeState() : super(_darkTheme, true);

  static final ThemeData _darkTheme = ThemeData(
    useMaterial3: false,
    brightness: Brightness.dark,
    primarySwatch:
        _createMaterialColor(HexColor.fromHex(APK_DETAILS['theme_color']!)),
    primaryColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
    scaffoldBackgroundColor: const Color(0xFF121212),
    appBarTheme: AppBarTheme(
      backgroundColor: const Color(0xFF1F1F1F),
      foregroundColor: HexColor.fromHex(APK_DETAILS['theme_forground_color']!),
      elevation: 0,
      iconTheme: IconThemeData(
        color: HexColor.fromHex(APK_DETAILS['theme_forground_color']!),
      ),
      titleTextStyle: TextStyle(
        color: HexColor.fromHex(APK_DETAILS['theme_forground_color']!),
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    cardTheme: CardThemeData(
      color: const Color(0xFF1E1E1E),
      elevation: 2,
      shadowColor: Colors.black.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
        foregroundColor:
            HexColor.fromHex(APK_DETAILS['theme_forground_color']!),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
        side: BorderSide(color: HexColor.fromHex(APK_DETAILS['theme_color']!)),
      ),
    ),
    textTheme: const TextTheme(
      displayLarge: TextStyle(color: Colors.white),
      displayMedium: TextStyle(color: Colors.white),
      displaySmall: TextStyle(color: Colors.white),
      headlineLarge: TextStyle(color: Colors.white),
      headlineMedium: TextStyle(color: Colors.white),
      headlineSmall: TextStyle(color: Colors.white),
      titleLarge: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
      titleMedium:
          TextStyle(color: Colors.white70, fontWeight: FontWeight.w500),
      titleSmall: TextStyle(color: Colors.white70, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(color: Colors.white70),
      bodyMedium: TextStyle(color: Colors.white70),
      bodySmall: TextStyle(color: Colors.white70),
      labelLarge: TextStyle(color: Colors.white70),
      labelMedium: TextStyle(color: Colors.white70),
      labelSmall: TextStyle(color: Colors.white60),
    ),
    iconTheme: const IconThemeData(color: Colors.white70),
    dividerTheme: const DividerThemeData(
      color: Colors.white24,
      thickness: 1,
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return HexColor.fromHex(APK_DETAILS['theme_color']!);
        }
        return Colors.grey;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return HexColor.fromHex(APK_DETAILS['theme_color']!)
              .withValues(alpha: 0.5);
        }
        return Colors.grey.withValues(alpha: 0.3);
      }),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: const Color(0xFF1F1F1F),
      selectedItemColor: HexColor.fromHex(APK_DETAILS['theme_color']!),
      unselectedItemColor: Colors.white54,
      type: BottomNavigationBarType.fixed,
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xFF2A2A2A),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide:
            BorderSide(color: HexColor.fromHex(APK_DETAILS['theme_color']!)),
      ),
    ),
  );
}
