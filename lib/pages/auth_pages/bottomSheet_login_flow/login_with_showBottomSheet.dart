import 'dart:convert';
import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:masterg/blocs/auth_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/auth_request/login_request.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/models/response/home_response/user_info_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/bottomSheet_login_flow/bottom_sheet_login_page.dart';
import 'package:masterg/pages/auth_pages/new_verify_otp.dart';
import 'package:masterg/pages/auth_pages/register_with_email.dart';
import 'package:masterg/pages/auth_pages/toggle.dart';
// import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
// import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:path_provider/path_provider.dart';

import '../../custom_pages/custom_widgets/CommonWebView.dart';

// ignore: must_be_immutable
class LoginBottomSheet extends StatefulWidget {
  bool isFromProfile;
  final bool showEdulystLogo;

  LoginBottomSheet({this.isFromProfile = false, required this.showEdulystLogo});

  @override
  _LoginBottomSheetState createState() => _LoginBottomSheetState();
}

class _LoginBottomSheetState extends State<LoginBottomSheet> {
  final phoneController = TextEditingController();
  FocusNode phoneFocus = FocusNode();
  bool isFocused = false;

  DateTime selectedDate = DateTime.now();
  final picker = ImagePicker();
  late String selectedImage;
  bool _isLoading = false;
  final _formKey = GlobalKey<FormState>();
  late String gender = "male";
  List<String> blockedPhone = ["1", "2", "3", "4", "0"];

  UserData userData = new UserData();

  bool loginwithemailEnabled = true;
  bool authBlocPageOpen = true;

  void initHive() async {
    await getApplicationDocumentsDirectory().then((value) {
      Hive.init(value.path);
      Hive.openBox(DB.CONTENT);
      Hive.openBox(DB.ANALYTICS);
      Hive.openBox(DB.TRAININGS);
      Hive.openBox('theme');
    });
  }

  @override
  void initState() {
    super.initState();
    initHive();
    phoneFocus.addListener(_onFocusChange);
    if (widget.isFromProfile) {
      userData = UserData.fromJson(json.decode(UserSession.userData!));
      phoneController.text = UserSession.phone!;
      gender = UserSession.gender!;
      UserSession.userImageUrl = userData.profileImage;
      Log.v(userData.branch);
    }
  }

  void _onFocusChange() {
    setState(() {
      isFocused = phoneFocus.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is LoginState && authBlocPageOpen)
              _handleLoginResponse(state);
          },
          child: Scaffold(
            backgroundColor: context.appColors.darkBackground,
            resizeToAvoidBottomInset: false,
            body: ScreenWithLoader(
              isContainerHeight: false,
              isLoading: _isLoading,
              body: _makeBody(),
            ),
          ),
        ));
  }

  _makeBody() {
    return Form(
        key: _formKey,
        child: Column(
          children: [
            Container(
              width: width(context),
              padding: EdgeInsets.all(10.0),
              child: APK_DETAILS['login_toggle'] == '0'
                  ? Container(
                      // margin: EdgeInsets.only(top: 40.0),
                      child: APK_DETAILS["package_name"] ==
                                      "com.singulariswow.mec" &&
                                  Preference.getString(
                                          Preference.USER_LOGIN_TYPE) ==
                                      'Mec Student' ||
                              Preference.getString(
                                      Preference.USER_LOGIN_TYPE) ==
                                  'Mec Faculty/Staff' ||
                              Preference.getString(
                                      Preference.USER_LOGIN_TYPE) ==
                                  'Mec Alumni'
                          ? Text(
                              tr('login_with_mec_id'),
                              textAlign: TextAlign.center,
                              style: Styles.textSemiBold(
                                  size: 14, color: context.appColors.textBlack),
                            )
                          : Center(
                              child: Text(
                                tr('login_with_email'),
                                style: Styles.textSemiBold(
                                    size: 14,
                                    color: context.appColors.textBlack),
                              ),
                            ),
                    )
                  : ToggleButton(
                      width: 200.0,
                      height: 47.0,
                      toggleBackgroundColor: Color(0xffF3F3F3),
                      toggleBorderColor: (Colors.grey[350])!,
                      toggleColor: (Colors.white),
                      activeTextColor: context.appColors.headingText,
                      inactiveTextColor: Colors.grey,
                      leftDescription: tr('email'),
                      rightDescription: tr('mobile'),
                      onLeftToggleActive: () {
                        print('left toggle activated');
                        setState(() {
                          loginwithemailEnabled = false;
                        });
                      },
                      onRightToggleActive: () {
                        setState(() {
                          loginwithemailEnabled = true;
                        });
                      },
                    ),
            ),
            loginwithemailEnabled
                ? SizedBox(
                    width: width(context), child: BottomSheetLoginScreen())
                : SizedBox(
                    width: width(context),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 20.0),
                          child: Container(
                            margin: EdgeInsets.symmetric(
                              horizontal: 18,
                            ),
                            child: TextFormField(
                              maxLength: APK_DETAILS["package_name"] ==
                                      "com.singulariswow.mec"
                                  ? 8
                                  : 10,
                              cursorColor: context.appColors.textBlack,
                              autofocus: false,
                              focusNode: phoneFocus,
                              controller: phoneController,
                              keyboardType: TextInputType.number,
                              style: Styles.bold(
                                color: context.appColors.headingText,
                                size: 14,
                              ),
                              inputFormatters: <TextInputFormatter>[
                                FilteringTextInputFormatter.allow(
                                    RegExp(r'[0-9]')),
                              ],
                              // maxLength: 10,
                              decoration: InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10.0),
                                  borderSide: BorderSide(
                                    color: context.appColors.grey3,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10.0),
                                  borderSide: BorderSide(
                                    color: context.appColors.grey3,
                                    width: 0.7,
                                  ),
                                ),
                                fillColor: context.appColors.grey3,
                                hintText: tr('mobile_number'),
                                hintStyle: Styles.regular(
                                  color: context.appColors.grey3,
                                  size: 14,
                                ),
                                isDense: true,
                                prefixIconConstraints:
                                    BoxConstraints(minWidth: 0, minHeight: 0),
                                prefixIcon: SizedBox(
                                  width: 70,
                                  child: InkWell(
                                    onTap: () {
                                      if (APK_DETAILS["package_name"] !=
                                          "com.singulariswow.mec") {
                                        Utility.showSnackBar(
                                            scaffoldContext: context,
                                            message: tr('mobile_indian_users'));
                                      }
                                    },
                                    child: Padding(
                                      padding: Utility().isRTL(context)
                                          ? EdgeInsets.only(right: 10.0)
                                          : EdgeInsets.only(left: 10.0),
                                      child: Row(
                                        children: [
                                          Text(
                                            APK_DETAILS["package_name"] ==
                                                    "com.singulariswow.mec"
                                                ? "+968 "
                                                : "+91 ",
                                            style: Styles.regular(
                                              color: context.appColors.grey3,
                                              size: 14,
                                            ),
                                          ),
                                          Image.asset(
                                            APK_DETAILS["package_name"] ==
                                                    "com.singulariswow.mec"
                                                ? 'assets/images/oman_flag.png'
                                                : 'assets/images/in_flag.png',
                                            width: 20,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        width: 0.7,
                                        color: context.appColors.grey3),
                                    borderRadius: BorderRadius.circular(10)),
                                helperStyle: Styles.regular(
                                    size: 14,
                                    color: context.appColors.grey3
                                        .withValues(alpha: 0.1)),
                                counterText: "",
                              ),
                              onChanged: (value) {
                                setState(() {});
                              },
                            ),
                          ),
                        ),
                        // SizedBox(height: 100),
                        InkWell(
                          onTap: () {
                            Preference.setBool(
                                Preference.LOGGEDIN_WITH_EMAIL, false);
                            if (phoneController.text
                                .toString()
                                .trim()
                                .isNotEmpty) {
                              if (APK_DETAILS["package_name"] ==
                                  "com.singulariswow.mec") {
                                if (phoneController.text.toString().length ==
                                    8) {
                                  if (blockedPhone.contains(phoneController.text
                                      .toString()
                                      .split('')
                                      .first))
                                    Utility.showSnackBar(
                                        scaffoldContext: context,
                                        message: tr('valid_phn_number'));
                                  else
                                    doLogin();
                                } else {
                                  Utility.showSnackBar(
                                      scaffoldContext: context,
                                      message: tr('valid_phn_number'));
                                }
                              } else {
                                if (phoneController.text.toString().length ==
                                    10) {
                                  if (blockedPhone.contains(phoneController.text
                                      .toString()
                                      .split('')
                                      .first))
                                    Utility.showSnackBar(
                                        scaffoldContext: context,
                                        message: tr('valid_phn_number'));
                                  else
                                    doLogin();
                                } else {
                                  Utility.showSnackBar(
                                      scaffoldContext: context,
                                      message: tr('valid_phn_number'));
                                }
                              }
                            } else {
                              Utility.showSnackBar(
                                  scaffoldContext: context,
                                  message: tr('enter_phone_number'));
                            }
                          },
                          child: Container(
                            margin: EdgeInsets.symmetric(
                              horizontal: 18,
                            ),
                            width: double.infinity,
                            height: MediaQuery.of(context).size.height * 0.06,
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10)),
                              gradient: LinearGradient(colors: [
                                context.appColors.gradientLeft,
                                context.appColors.gradientRight,
                              ]),
                              boxShadow: [
                                BoxShadow(
                                    color: context.appColors.gradientRight
                                        .withValues(alpha: 0.3),
                                    offset: Offset(2.5, 10),
                                    blurRadius: 20)
                              ],
                            ),
                            child: Center(
                              child: Text(
                                'get_otp',
                                style: Styles.bold(
                                  size: 16,
                                  color: context.appColors.textWhite,
                                ),
                              ).tr(),
                            ),
                          ),
                          //
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 30.0),
                          child: Column(children: [
                            Text(
                              'not_registered',
                              style: Styles.regular(
                                  size: 14,
                                  color: context.appColors.headingText),
                            ).tr(),
                            SizedBox(height: 10),
                            ShaderMask(
                                blendMode: BlendMode.srcIn,
                                shaderCallback: (Rect bounds) {
                                  return LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: <Color>[
                                        context.appColors.gradientRight,
                                        context.appColors.gradientRight
                                      ]).createShader(bounds);
                                },
                                child: InkWell(
                                  onTap: () {
                                    if (APK_DETAILS["register_in_app"] == "0") {
                                      Navigator.push(
                                          context,
                                          NextPageRoute(CommonWebView(
                                            url: "https://mec.edu.om/" +
                                                "${Preference.getString(Preference.LANGUAGE)}",
                                          )));
                                    } else {
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  RegisterScreen()));
                                    }
                                  },
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text('register_now',
                                          style: Styles.getBoldThemeStyle(
                                            context,
                                            size: 14,
                                          )).tr(),
                                      Icon(
                                        Icons.arrow_forward_ios_outlined,
                                        size: 16,
                                      ),
                                    ],
                                  ),
                                ))
                          ]),
                        ),
                      ],
                    ),
                  ),
          ],
        ));
  }

  void _handleLoginResponse(LoginState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          authBlocPageOpen = false;
          Navigator.push(
              context,
              NextPageRoute(NewVerifyOtp(
                phoneController.text.toString(),
              ))).then((value) => setState(() {
                authBlocPageOpen = true;
              }));
          _isLoading = false;
          break;

        case ApiStatus.ERROR:
          setState(() {
            _isLoading = false;
          });

          FirebaseAnalytics.instance.logEvent(name: 'login_page', parameters: {
            "login_failed": "true",
            "ERROR": '${loginState.response?.error?[0]}',
          });

          if (loginState.response?.error?[0] != null) {
            AlertsWidget.alertWithOkBtn(
                context: context,
                text: loginState.response?.error?[0],
                onOkClick: () {
                  FocusScope.of(context).unfocus();
                });
          } else {
            AlertsWidget.alertWithOkBtn(
                context: context,
                text: 'something_went_wrong'.tr(),
                onOkClick: () {
                  FocusScope.of(context).unfocus();
                });
          }
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void doLogin() {
    setState(() {
      _isLoading = true;
    });
    if (!_formKey.currentState!.validate()) return;

    BlocProvider.of<AuthBloc>(context).add(LoginUser(
        request: LoginRequest(
            mobileNo: phoneController.text.toString().trim(),
            mobile_exist_skip: '1')));
  }

  void showFileChoosePopup() {
    //  Utility.hideKeyboard();
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black45,
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        // return object of type Dialog
        return SimpleDialog(
          backgroundColor: context.appColors.primary,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          children: <Widget>[
            Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(18)),
                color: context.appColors.primary,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                      padding: EdgeInsets.all(10),
                      child: Text(
                        'photo_alert',
                        style: Styles.regularWhite(size: 18),
                      ).tr()),
                  InkWell(
                      child: Padding(
                        padding: EdgeInsets.all(10),
                        child: Text('take_a_picture',
                                style: Styles.regularWhite(size: 16))
                            .tr(),
                      ),
                      onTap: () {
                        _getImages(ImageSource.camera).then((value) {
                          Navigator.pop(context);
                          if (value.isEmpty) return;
                          setState(() {
                            UserSession.userImageUrl = null;
                            selectedImage = value;
                          });
                        });
                      }),
                  InkWell(
                      child: Padding(
                          padding: EdgeInsets.all(10),
                          child: Text('pick_from_gallery',
                                  style: Styles.regularWhite(size: 16))
                              .tr()),
                      onTap: () async {
                        _getImages(ImageSource.gallery).then((value) {
                          Navigator.pop(context);
                          if (value.isEmpty) return;
                          setState(() {
                            UserSession.userImageUrl = null;
                            selectedImage = value;
                          });
                        });
                      }),
                ],
              ),
            )
          ],
        );
      },
    );
  }

  /*Future<String> _getImages(ImageSource source) async {
    final picker = ImagePicker();
    PickedFile? pickedFile = await picker.getImage(source: source);
    if (pickedFile != null)
      return pickedFile.path;
    else if (Platform.isAndroid) {
      final LostData response = await picker.getLostData();
      if (response.file != null) {
        return response.file!.path;
      }
    }
    return "";
  }*/

  Future<String> _getImages(ImageSource source) async {
    final picker = ImagePicker();
    final XFile? pickedFile = await picker.pickImage(source: source);

    if (pickedFile != null) {
      return pickedFile.path;
    } else if (Platform.isAndroid) {
      final LostDataResponse response = await picker.retrieveLostData();
      if (response.file != null) {
        return response.file!.path;
      }
    }
    return "";
  }
}
