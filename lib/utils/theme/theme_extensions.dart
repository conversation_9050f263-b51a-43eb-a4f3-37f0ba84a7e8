import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/theme/theme_bloc.dart';
import '../config.dart';
import '../resource/colors.dart';

/// Extension on BuildContext to provide easy access to theme-related properties
extension ThemeExtension on BuildContext {
  /// Get the current theme data
  ThemeData get theme => Theme.of(this);

  /// Get the current color scheme
  ColorScheme get colorScheme => theme.colorScheme;

  /// Get the current text theme
  TextTheme get textTheme => theme.textTheme;

  /// Check if the current theme is dark mode
  bool get isDarkMode {
    final themeState = read<ThemeBloc>().state;
    return themeState.isDarkMode;
  }

  /// Get theme-aware colors that adapt to light/dark mode

  AppColors get appColors => AppColors(isDarkMode);

  /// Get the primary brand color
  Color get primaryDark => HexColor.fromHex(APK_DETAILS['theme_color']!);

  /// Get the primary foreground color
  Color get primaryForegroundColor =>
      HexColor.fromHex(APK_DETAILS['theme_forground_color']!);

  /// Get theme-aware gradient colors
  /// Light theme: uses original brand gradient colors from config
  /// Dark theme: uses lighter, more muted versions for better visibility on dark backgrounds
  List<Color> get gradientColors => [
        gradientLeftColor,
        gradientRightColor,
      ];

  /// Get theme-aware left gradient color
  Color get gradientLeftColor =>
      //  isDarkMode
      //     ? _getDarkModeGradientColor(APK_DETAILS['theme_color']!)
      //     :
      HexColor.fromHex(APK_DETAILS['theme_color']!);

  /// Get theme-aware right gradient color
  Color get gradientRightColor =>
      //  isDarkMode
      //     ? _getDarkModeGradientColor(APK_DETAILS['theme_color']!)
      //     :
      HexColor.fromHex(APK_DETAILS['theme_color']!);

  /// Helper method to create appropriate dark mode gradient colors
  /// Converts brand colors to lighter, more muted versions suitable for dark themes
  // Color _getDarkModeGradientColor(String hexColor) {
  //   final lightColor = HexColor.fromHex(hexColor);
  //   // Create a lighter, more muted version by blending with white and reducing saturation
  //   final hsl = HSLColor.fromColor(lightColor);
  //   return hsl
  //       .withLightness((hsl.lightness + 0.3).clamp(0.0, 1.0))
  //       .withSaturation((hsl.saturation * 0.7).clamp(0.0, 1.0))
  //       .toColor();
  // }

  /// Get theme-aware surface color
  Color get surfaceColor => isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;

  /// Get theme-aware background color
  Color get backgroundColor =>
      isDarkMode ? const Color(0xFF121212) : Colors.white;

  /// Get theme-aware text color for body text
  Color get bodyTextColor =>
      isDarkMode ? Colors.white70 : ColorConstants.bodyText;

  /// Get theme-aware text color for headings
  Color get headingTextColor =>
      isDarkMode ? Colors.white : ColorConstants.headingTitle;

  /// Get theme-aware text color for sub-headings
  Color get subHeadingTextColor =>
      isDarkMode ? Colors.white70 : ColorConstants.subHeadingTitle;

  /// Get theme-aware divider color
  Color get dividerColor =>
      isDarkMode ? Colors.white24 : ColorConstants.dividerColor1;

  /// Get theme-aware icon color
  Color get iconColor => isDarkMode ? Colors.white70 : ColorConstants.bodyText;

  /// Get theme-aware hint text color
  Color get hintTextColor =>
      isDarkMode ? Colors.white60 : ColorConstants.hintGrey;

  /// Get theme-aware disabled color
  Color get disabledColor =>
      isDarkMode ? Colors.white38 : ColorConstants.disableColor;

  /// Get theme-aware error color
  Color get errorColor => isDarkMode ? Colors.redAccent : ColorConstants.red;

  /// Get theme-aware success color
  Color get successColor =>
      isDarkMode ? Colors.greenAccent : ColorConstants.green;

  /// Get theme-aware warning color
  Color get warningColor =>
      isDarkMode ? Colors.orangeAccent : ColorConstants.activeTab;

  /// Get theme-aware dashboard background color
  Color get dashboardBackgroundColor =>
      isDarkMode ? const Color(0xFF121212) : ColorConstants.dashboardBgColor;

  /// Get theme-aware dialog background color
  Color get dialogBackgroundColor =>
      isDarkMode ? const Color(0xFF2A2A2A) : Colors.white;

  /// Get theme-aware card color
  Color get cardColor => isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;

  /// Get theme-aware border color
  Color get borderColor =>
      isDarkMode ? Colors.white24 : ColorConstants.dividerColor1;

  /// Get theme-aware input field background color
  Color get inputFieldBackgroundColor =>
      isDarkMode ? const Color(0xFF2A2A2A) : ColorConstants.textFieldBg;

  /// Get theme-aware input field border color
  Color get inputFieldBorderColor =>
      isDarkMode ? Colors.white38 : ColorConstants.dividerColor1;

  /// Get theme-aware input field focused border color
  Color get inputFieldFocusedBorderColor =>
      isDarkMode ? primaryDark : primaryDark;

  /// Get theme-aware button background color
  Color get buttonBackgroundColor =>
      isDarkMode ? const Color(0xFF2A2A2A) : Colors.white;

  /// Get theme-aware secondary button background color
  Color get secondaryButtonBackgroundColor =>
      isDarkMode ? Colors.white24 : ColorConstants.grey;

  /// Get theme-aware badge background color
  Color get badgeBackgroundColor =>
      isDarkMode ? const Color(0xFF3A3A3A) : ColorConstants.grey;

  /// Get theme-aware status bar color
  Color get statusBarColor =>
      isDarkMode ? const Color(0xFF000000) : primaryDark;

  /// Get theme-aware navigation bar color
  Color get navigationBarColor =>
      isDarkMode ? const Color(0xFF1F1F1F) : Colors.white;

  /// Get theme-aware selection color
  Color get selectionColor => isDarkMode
      ? primaryDark.withValues(alpha: 0.3)
      : primaryDark.withValues(alpha: 0.2);

  /// Get theme-aware splash color
  Color get splashColor => isDarkMode ? Colors.white24 : Colors.black12;

  /// Get theme-aware highlight color
  Color get highlightColor => isDarkMode ? Colors.white12 : Colors.black12;

  /// Get theme-aware shimmer base color
  Color get shimmerBaseColor =>
      isDarkMode ? Colors.grey[800]! : const Color(0xffe6e4e6);

  /// Get theme-aware shimmer highlight color
  Color get shimmerHighlightColor =>
      isDarkMode ? Colors.grey[700]! : const Color(0xffeaf0f3);

  /// Get theme-aware assessment colors
  Color get answeredColor =>
      isDarkMode ? Colors.orange[300]! : ColorConstants.answered;
  Color get notAnsweredColor =>
      isDarkMode ? Colors.yellow[300]! : ColorConstants.notAnswered;
  Color get reviewedColor =>
      isDarkMode ? Colors.teal[300]! : ColorConstants.selectedGreen;
  Color get answeredReviewsColor =>
      isDarkMode ? Colors.blue[300]! : ColorConstants.answeredReviews;
  Color get selectedGreenColor =>
      isDarkMode ? Colors.teal[300]! : ColorConstants.selectedGreen;

  /// Get theme-aware skill level colors
  Color get noviceColor =>
      isDarkMode ? Colors.yellow[300]! : ColorConstants.novoice;
  Color get learnerColor =>
      isDarkMode ? Colors.orange[300]! : ColorConstants.learner;
  Color get masterColor =>
      isDarkMode ? Colors.deepOrange[300]! : ColorConstants.master;
  Color get expertColor =>
      isDarkMode ? Colors.blue[300]! : ColorConstants.expert;
  Color get leaderColor =>
      isDarkMode ? Colors.indigo[300]! : ColorConstants.leader;

  /// Get theme-aware job-related colors
  Color get jobBackgroundColor =>
      isDarkMode ? const Color(0xFF1E1E1E) : ColorConstants.jobBgColor;
  Color get highlightCard1Color =>
      isDarkMode ? Colors.teal[400]! : ColorConstants.green;
  Color get highlightCard2Color =>
      isDarkMode ? Colors.orange[400]! : ColorConstants.highlightsCardColor2;

  /// Get theme-aware gradient colors
  Color get gradientRedColor =>
      isDarkMode ? Colors.red[400]! : ColorConstants.gradientRed;
  Color get gradientOrangeColor =>
      isDarkMode ? Colors.orange[400]! : ColorConstants.gradientOrange;
  Color get progressBarTealColor =>
      isDarkMode ? Colors.teal[400]! : ColorConstants.green;

  /// Get theme-aware special colors
  Color get pillBackgroundColor =>
      isDarkMode ? const Color(0xFF3A3A2A) : ColorConstants.pillBg;
  Color get documentQuoteBackgroundColor =>
      isDarkMode ? const Color(0xFF2A2A1A) : ColorConstants.documentQuoteBg;
  Color get otpTextColor =>
      isDarkMode ? Colors.blue[200]! : ColorConstants.otpText;

  /// Get theme-aware button colors
  Color get continueButtonColor =>
      isDarkMode ? Colors.teal[600]! : ColorConstants.continueColor;
  Color get blueButtonColor =>
      isDarkMode ? Colors.blue[400]! : ColorConstants.bgBlueBtn;
  Color get darkBlueButtonColor =>
      isDarkMode ? Colors.blue[600]! : ColorConstants.bgDarkBlueBtn;
  Color get darkBlueButton2Color =>
      isDarkMode ? Colors.blue[700]! : ColorConstants.bgDarkBlueBtn2;

  /// Get theme-aware portfolio colors
  Color get buildPortfolio1Color =>
      isDarkMode ? Colors.orange[400]! : ColorConstants.buildPortfolio1;
  Color get buildPortfolio2Color =>
      isDarkMode ? const Color(0xFF2A2A2A) : ColorConstants.buildPortfolio2;

  /// Get theme-aware subtle overlay color for backgrounds and borders
  /// Light theme: semi-transparent dark gray (12% opacity)
  /// Dark theme: semi-transparent light gray (15% opacity) for better visibility on dark backgrounds
  Color get subtleOverlayColor =>
      isDarkMode ? const Color(0x26FFFFFF) : const Color(0x1E1C1C1C);

  /// Get theme-aware background surface color (light/dark)
  Color get backgroundSurface => appColors.backgroundSurface;
}

/// Theme-aware color class that provides colors based on the current theme mode
class AppColors {
  final bool isDarkMode;

  const AppColors(this.isDarkMode);

  /// Primary brand color (always the same regardless of theme)
  Color get primary => HexColor.fromHex(APK_DETAILS['theme_color']!);

  /// Primary foreground color (always the same regardless of theme)
  Color get primaryForeground =>
      HexColor.fromHex(APK_DETAILS['theme_forground_color']!);

  /// Theme-aware gradient colors
  /// Light theme: uses original brand gradient colors from config
  /// Dark theme: uses lighter, more muted versions for better visibility on dark backgrounds
  List<Color> get gradientColors => [
        gradientLeft,
        gradientRight,
      ];

  Color get pureBlack => Colors.black;

  Color get textWhite => isDarkMode ? Colors.black : Colors.white;
  Color get textBlack => isDarkMode ? Colors.white : Colors.black;

  /// Theme-aware left gradient color
  Color get gradientLeft =>
      // isDarkMode
      //     ? _getDarkModeGradientColor(APK_DETAILS['theme_color']!)
      //     :
      HexColor.fromHex(APK_DETAILS['theme_color']!);

  /// Theme-aware right gradient color
  Color get gradientRight =>
      //  isDarkMode
      //     ? _getDarkModeGradientColor(APK_DETAILS['theme_color']!)
      //     :
      HexColor.fromHex(APK_DETAILS['theme_color']!);

  /// Helper method to create appropriate dark mode gradient colors
  /// Converts brand colors to lighter, more muted versions suitable for dark themes
  // Color _getDarkModeGradientColor(String hexColor) {
  //   final lightColor = HexColor.fromHex(hexColor);
  //   // Create a lighter, more muted version by blending with white and reducing saturation
  //   final hsl = HSLColor.fromColor(lightColor);
  //   return hsl
  //       .withLightness((hsl.lightness + 0.3).clamp(0.0, 1.0))
  //       .withSaturation((hsl.saturation * 0.7).clamp(0.0, 1.0))
  //       .toColor();
  // }

  /// Surface color that adapts to theme
  Color get surface => isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;

  /// Background color that adapts to theme
  Color get background => isDarkMode ? const Color(0xFF121212) : Colors.white;

  /// Card background color that adapts to theme
  Color get cardBackground =>
      isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;

  /// App bar background color that adapts to theme
  Color get appBarBackground => isDarkMode ? const Color(0xFF1F1F1F) : primary;

  /// Text field background color that adapts to theme
  Color get textFieldBackground =>
      isDarkMode ? const Color(0xFF2A2A2A) : ColorConstants.textFieldBg;

  /// Body text color that adapts to theme
  Color get bodyText => isDarkMode ? Colors.white70 : ColorConstants.bodyText;

  /// Heading text color that adapts to theme
  Color get headingText =>
      isDarkMode ? Colors.white : ColorConstants.headingTitle;

  /// Sub-heading text color that adapts to theme
  Color get subHeadingText =>
      isDarkMode ? Colors.white70 : ColorConstants.subHeadingTitle;

  /// Label text color that adapts to theme
  Color get labelText => isDarkMode ? Colors.white60 : ColorConstants.lebelText;

  /// Divider color that adapts to theme
  Color get divider =>
      isDarkMode ? Colors.white24 : ColorConstants.dividerColor1;

  /// Icon color that adapts to theme
  Color get icon => isDarkMode ? Colors.white70 : ColorConstants.bodyText;

  /// Hint text color that adapts to theme
  Color get hintText => isDarkMode ? Colors.white60 : ColorConstants.hintGrey;

  /// Disabled color that adapts to theme
  Color get disabled =>
      isDarkMode ? Colors.white38 : ColorConstants.disableColor;

  /// Error color that adapts to theme
  Color get error => isDarkMode ? Colors.redAccent : ColorConstants.red;

  /// Success color that adapts to theme
  Color get success => isDarkMode ? Colors.greenAccent : ColorConstants.green;
  Color get green => isDarkMode ? Colors.greenAccent : ColorConstants.green;

  /// Warning color that adapts to theme
  Color get warning =>
      isDarkMode ? Colors.orangeAccent : ColorConstants.activeTab;

  /// Bottom navigation background color that adapts to theme
  Color get bottomNavBackground =>
      isDarkMode ? const Color(0xFF1F1F1F) : Colors.white;

  /// Bottom navigation selected item color
  Color get bottomNavSelected => primary;

  /// Bottom navigation unselected item color that adapts to theme
  Color get bottomNavUnselected =>
      isDarkMode ? Colors.white54 : ColorConstants.inactiveTab;

  /// Shadow color that adapts to theme
  Color get shadow => isDarkMode
      ? Colors.black.withValues(alpha: 0.3)
      : Colors.black.withValues(alpha: 0.1);

  /// Overlay color that adapts to theme
  Color get overlay => isDarkMode
      ? Colors.white.withValues(alpha: 0.1)
      : Colors.black.withValues(alpha: 0.1);

  /// Dashboard background color that adapts to theme
  Color get dashboardBackground =>
      isDarkMode ? const Color(0xFF121212) : ColorConstants.dashboardBgColor;

  /// Dialog background color that adapts to theme
  Color get dialogBackground =>
      isDarkMode ? const Color(0xFF2A2A2A) : Colors.white;

  /// Border color that adapts to theme
  Color get border =>
      isDarkMode ? Colors.white24 : ColorConstants.dividerColor1;

  /// Input field border color that adapts to theme
  Color get inputFieldBorder =>
      isDarkMode ? Colors.white38 : ColorConstants.dividerColor1;

  /// Input field focused border color that adapts to theme
  Color get inputFieldFocusedBorder => isDarkMode ? primary : primary;

  /// Secondary button background color that adapts to theme
  Color get secondaryButtonBackground =>
      isDarkMode ? Colors.white24 : ColorConstants.grey;

  /// Badge background color that adapts to theme
  Color get badgeBackground =>
      isDarkMode ? const Color(0xFF3A3A3A) : ColorConstants.grey;

  /// Status bar color that adapts to theme
  Color get statusBar => isDarkMode ? const Color(0xFF000000) : primary;

  /// Navigation bar color that adapts to theme
  Color get navigationBar =>
      isDarkMode ? const Color(0xFF1F1F1F) : Colors.white;

  /// Selection color that adapts to theme
  Color get selection => isDarkMode
      ? primary.withValues(alpha: 0.3)
      : primary.withValues(alpha: 0.2);

  /// Splash color that adapts to theme
  Color get splash => isDarkMode ? Colors.white24 : Colors.black12;

  /// Highlight color that adapts to theme
  Color get highlight => isDarkMode ? Colors.white12 : Colors.black12;

  /// Shimmer colors that adapt to theme
  Color get shimmerBase =>
      isDarkMode ? Colors.grey[800]! : const Color(0xffe6e4e6);
  Color get shimmerHighlight =>
      isDarkMode ? Colors.grey[700]! : const Color(0xffeaf0f3);

  /// Assessment colors that adapt to theme
  Color get answered =>
      isDarkMode ? Colors.orange[300]! : ColorConstants.answered;
  Color get notAnswered =>
      isDarkMode ? Colors.yellow[300]! : ColorConstants.notAnswered;
  Color get reviewed =>
      isDarkMode ? Colors.teal[300]! : ColorConstants.selectedGreen;
  Color get answeredReviews =>
      isDarkMode ? Colors.blue[300]! : ColorConstants.answeredReviews;
  Color get selectedGreen =>
      isDarkMode ? Colors.teal[300]! : ColorConstants.selectedGreen;

  /// Skill level colors that adapt to theme
  Color get novoice =>
      isDarkMode ? Colors.yellow[300]! : ColorConstants.novoice;
  Color get learner =>
      isDarkMode ? Colors.orange[300]! : ColorConstants.learner;
  Color get master =>
      isDarkMode ? Colors.deepOrange[300]! : ColorConstants.master;
  Color get expert => isDarkMode ? Colors.blue[300]! : ColorConstants.expert;
  Color get leader => isDarkMode ? Colors.indigo[300]! : ColorConstants.leader;

  Color get grey4 => isDarkMode ? Colors.grey[600]! : ColorConstants.grey4;
  Color get grey5 => isDarkMode ? Colors.grey[700]! : ColorConstants.grey5;
  Color get grey6 => isDarkMode ? Colors.grey[800]! : ColorConstants.grey6;
  Color get courseBg =>
      isDarkMode ? Colors.grey[900]! : ColorConstants.courseBg;
  Color get sectionDivider =>
      isDarkMode ? Colors.grey[800]! : ColorConstants.sectionDivider;
  Color get dividerAlt =>
      isDarkMode ? Colors.grey[700]! : ColorConstants.divider;
  Color get notificationDateGrey =>
      isDarkMode ? Colors.grey[500]! : ColorConstants.notificationDateGrey;
  Color get greyOutline =>
      isDarkMode ? Colors.grey[300]! : ColorConstants.greyOutline;
  Color get purple => isDarkMode ? Colors.purple[200]! : ColorConstants.purple;
  Color get customPurple =>
      isDarkMode ? const Color(0xFFB3AFFF) : const Color(0xff978FED);
  Color get starcolour =>
      isDarkMode ? Colors.red[200]! : ColorConstants.starColor;
  Color get pillBg => isDarkMode ? Colors.grey[700]! : ColorConstants.pillBg;
  Color get allBg => isDarkMode ? Colors.grey[600]! : ColorConstants.allBg;
  Color get unselectedButton =>
      isDarkMode ? Colors.grey[400]! : ColorConstants.unselectedButton;
  Color get iconBgGrey =>
      isDarkMode ? Colors.grey[800]! : ColorConstants.iconBgGrey;

  /// Job-related colors that adapt to theme
  Color get jobBackground =>
      isDarkMode ? const Color(0xFF1E1E1E) : ColorConstants.jobBgColor;
  Color get highlightCard1 =>
      isDarkMode ? Colors.teal[400]! : ColorConstants.green;
  Color get highlightCard2 =>
      isDarkMode ? Colors.orange[400]! : ColorConstants.highlightsCardColor2;

  /// Gradient colors that adapt to theme
  Color get gradientRed =>
      isDarkMode ? Colors.red[400]! : ColorConstants.gradientRed;
  Color get gradientOrange =>
      isDarkMode ? Colors.orange[400]! : ColorConstants.gradientOrange;
  Color get progressBarTeal =>
      isDarkMode ? Colors.teal[400]! : ColorConstants.green;

  /// Special colors that adapt to theme
  Color get pillBackground =>
      isDarkMode ? const Color(0xFF3A3A2A) : ColorConstants.pillBg;
  Color get documentQuoteBackground =>
      isDarkMode ? const Color(0xFF2A2A1A) : ColorConstants.documentQuoteBg;
  Color get otpText => isDarkMode ? Colors.blue[200]! : ColorConstants.otpText;

  /// Button colors that adapt to theme
  Color get continueButton =>
      isDarkMode ? Colors.teal[600]! : ColorConstants.continueColor;
  Color get blueButton =>
      isDarkMode ? Colors.blue[400]! : ColorConstants.bgBlueBtn;
  Color get darkBlueButton =>
      isDarkMode ? Colors.blue[600]! : ColorConstants.bgDarkBlueBtn;
  Color get darkBlueButton2 =>
      isDarkMode ? Colors.blue[700]! : ColorConstants.bgDarkBlueBtn2;

  /// Portfolio colors that adapt to theme
  Color get buildPortfolio1 =>
      isDarkMode ? Colors.orange[400]! : ColorConstants.buildPortfolio1;
  Color get buildPortfolio2 =>
      isDarkMode ? const Color(0xFF2A2A2A) : ColorConstants.buildPortfolio2;

  /// Subtle overlay color for backgrounds and borders that adapts to theme
  /// Light theme: semi-transparent dark gray (12% opacity)
  /// Dark theme: semi-transparent light gray (15% opacity) for better visibility on dark backgrounds
  Color get subtleOverlay =>
      isDarkMode ? const Color(0x26FFFFFF) : const Color(0x1E1C1C1C);

  /// Background surface color that adapts to theme (light/dark)
  Color get backgroundSurface =>
      isDarkMode ? const Color(0xFF23272F) : const Color(0xffE9ECF3);

  Color get primaryColorLight =>
      isDarkMode ? Colors.grey[700]! : ColorConstants.primaryColorLight;
  Color get primaryBlue =>
      isDarkMode ? Colors.lightBlue[300]! : ColorConstants.primaryBlue;
  Color get backgroundColorAlt =>
      isDarkMode ? Colors.grey[900]! : ColorConstants.backgroundColor;
  Color get headingTitle =>
      isDarkMode ? Colors.white : ColorConstants.headingTitle;
  Color get subHeadingTitle =>
      isDarkMode ? Colors.grey[400]! : ColorConstants.subHeadingTitle;
  Color get lebelText =>
      isDarkMode ? Colors.grey[600]! : ColorConstants.lebelText;
  Color get dividerColor2 =>
      isDarkMode ? Colors.grey[800]! : ColorConstants.dividerColor2;
  Color get darkBackground =>
      isDarkMode ? Colors.grey[900]! : ColorConstants.darkBackground;
  Color get darkButton =>
      isDarkMode ? Colors.grey[800]! : ColorConstants.darkButton;
  Color get wowPrimaryColor =>
      isDarkMode ? Colors.orange[300]! : ColorConstants.wowPrimaryColor;
  Color get primaryDark =>
      isDarkMode ? Colors.indigo[300]! : ColorConstants.primaryDark;
  Color get accentColor => isDarkMode ? Colors.grey[50]! : ColorConstants.white;
  Color get textFieldBg =>
      isDarkMode ? Colors.grey[800]! : ColorConstants.textFieldBg;
  Color get pendingGrey =>
      isDarkMode ? Colors.grey[500]! : ColorConstants.pendingGrey;
  Color get grey => isDarkMode ? Colors.grey[600]! : ColorConstants.grey;
  Color get darGrey => isDarkMode ? Colors.grey[300]! : ColorConstants.darkGrey;
  Color get green1 => isDarkMode ? Colors.green[300]! : ColorConstants.green;
  Color get redBg => isDarkMode ? Colors.red[300]! : ColorConstants.redBg;
  Color get bottomGrey =>
      isDarkMode ? Colors.grey[850]! : ColorConstants.bottomGrey;
  Color get inactiveTab =>
      isDarkMode ? Colors.grey[500]! : ColorConstants.inactiveTab;
  Color get yellowActiveButton =>
      isDarkMode ? Colors.yellow[300]! : ColorConstants.yellowActiveTab;
  Color get orange3 =>
      isDarkMode ? Colors.orange[300]! : ColorConstants.orange3;
  Color get viewAll => isDarkMode ? Colors.amber[200]! : ColorConstants.viewAll;
  Color get orange4 =>
      isDarkMode ? Colors.orange[400]! : ColorConstants.orange4;
  Color get startGreyBg =>
      isDarkMode ? Colors.grey[700]! : ColorConstants.startGreyBg;
  Color get activeTabUnderline =>
      isDarkMode ? Colors.blue[300]! : ColorConstants.activeTabUnderline;
  Color get textDarkBlack =>
      isDarkMode ? Colors.grey[50]! : ColorConstants.textDarkBlack;
  Color get searchFilled =>
      isDarkMode ? Colors.blue[700]! : ColorConstants.searchFilled;
  Color get selectedPage =>
      isDarkMode ? Colors.amber[300]! : ColorConstants.selectedPage;
  Color get unselectedPage =>
      isDarkMode ? Colors.blue[300]! : ColorConstants.unselectedPage;

  /// Additional grey colors that adapt to theme
  Color get grey1 => isDarkMode ? Colors.grey[300]! : ColorConstants.grey1;
  Color get grey2 => isDarkMode ? Colors.grey[400]! : ColorConstants.grey2;
  Color get grey3 => isDarkMode ? Colors.grey[500]! : ColorConstants.grey3;
  Color get grey10 => isDarkMode ? Colors.grey[400]! : ColorConstants.grey10;

  ///  Added for complete coverage from ColorConstants
  Color get bgLightGrey =>
      isDarkMode ? const Color(0xFF2A2A3A) : ColorConstants.bgLigtGrey;

  /// Additional special colors that adapt to theme
  Color get darkGrey =>
      isDarkMode ? Colors.grey[200]! : ColorConstants.darkGrey;
  Color get iconBackgroundGrey =>
      isDarkMode ? Colors.grey[700]! : ColorConstants.iconBgGrey;
  Color get lightGreyBackground =>
      isDarkMode ? const Color(0xFF2A2A3A) : ColorConstants.bgLigtGrey;
  Color get startGreyBackground =>
      isDarkMode ? Colors.grey[700]! : ColorConstants.startGreyBg;
  Color get allBackground =>
      isDarkMode ? Colors.grey[600]! : ColorConstants.allBg;

  /// Course and section colors that adapt to theme
  Color get courseBackground =>
      isDarkMode ? Colors.grey[800]! : ColorConstants.courseBg;
  Color get starColor =>
      isDarkMode ? Colors.red[300]! : ColorConstants.starColor;

  /// Additional utility colors that adapt to theme
  Color get cyan => isDarkMode ? Colors.cyan[300]! : ColorConstants.primaryBlue;
  Color get orange => isDarkMode ? Colors.orange[300]! : ColorConstants.orange;
  Color get darkBlue =>
      isDarkMode ? Colors.blue[200]! : ColorConstants.darkBlue;
  Color get textDarkBlue =>
      isDarkMode ? Colors.blue[200]! : ColorConstants.textDarkBlue;
  Color get yellow => isDarkMode ? Colors.yellow[300]! : ColorConstants.yellow;

  Color get redBackground =>
      isDarkMode ? Colors.red[400]! : ColorConstants.redBg;
  Color get listColor =>
      isDarkMode ? const Color(0xFF2A1A1A) : ColorConstants.listColor;

  // --- Missing colors from ColorConstants ---

  /// Alternate divider color
  Color get divider2 =>
      isDarkMode ? Colors.white12 : ColorConstants.dividerColor2;

  /// A specific dark background color

  /// WOW primary color, same for both themes
  Color get wowPrimary => ColorConstants.wowPrimaryColor;

  /// Bottom grey background

  /// Alternate general background color
  Color get bgColor =>
      isDarkMode ? const Color(0xFF121212) : ColorConstants.bgColor;

  /// Alternate divider color

  /// Alternate grey background
  Color get bgGrey =>
      isDarkMode ? const Color(0xFF2E2E2E) : ColorConstants.bgGrey;

  Color get headingPrimaryColor =>
      isDarkMode ? Colors.white : ColorConstants.headingTitle;

  /// Blue screen background color
  Color get bgBlueScreen =>
      isDarkMode ? ColorConstants.bgBlueScreen : Colors.blue[50]!;

  /// Special green color
  Color get green0e =>
      isDarkMode ? Colors.tealAccent[200]! : ColorConstants.selectedGreen;

  /// Steel blue color
  Color get blue5f =>
      isDarkMode ? Colors.blueGrey[300]! : ColorConstants.color5f6687;

  /// Selected tab color
  Color get tabSelected =>
      isDarkMode ? Colors.orange[300]! : ColorConstants.selected;

  /// Alternate AppBar color
  Color get appBarColorAlt =>
      isDarkMode ? const Color(0xFF205E9C) : ColorConstants.appbarColor;

  /// Alternate button color
  Color get buttonColorAlt =>
      isDarkMode ? Colors.blueAccent[100]! : ColorConstants.buttoncolor;

  /// Dashboard apply button color
  Color get dashboardApplyColor =>
      isDarkMode ? Colors.indigo[200]! : ColorConstants.dashboardApplyColor;

  /// The hardcoded primary color constant.
  Color get primaryColorConst =>
      isDarkMode ? Colors.indigo[200]! : ColorConstants.primaryDark;

  /// The hardcoded dark primary color constant, same as primaryColorConst.
  Color get primaryColorDarkConst =>
      isDarkMode ? Colors.indigo[200]! : ColorConstants.primaryDark;

  /// The accent color constant (white).

  /// A specific shadow color constant.
  Color get shadowColorConst => isDarkMode
      ? Colors.black.withValues(alpha: 0.5)
      : ColorConstants.shadowColor;
}

/// Helper class for theme-aware styling

/// Helper class for theme-aware styling
class ThemeHelper {
  /// Get a BoxDecoration with theme-aware colors
  static BoxDecoration getCardDecoration(
    BuildContext context, {
    double borderRadius = 8.0,
    bool withShadow = true,
  }) {
    return BoxDecoration(
      color: context.surfaceColor,
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: withShadow
          ? [
              BoxShadow(
                color: context.appColors.shadow,
                offset: const Offset(0, 2),
                blurRadius: 4,
              ),
            ]
          : null,
    );
  }

  /// Get a gradient decoration with brand colors
  static BoxDecoration getGradientDecoration(
    BuildContext context, {
    double borderRadius = 8.0,
    List<Color>? colors,
  }) {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(borderRadius),
      gradient: LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: colors ?? context.gradientColors,
      ),
    );
  }

  /// Get theme-aware text style
  static TextStyle getTextStyle(
    BuildContext context, {
    required TextStyleType type,
    Color? color,
    FontWeight? fontWeight,
    double? fontSize,
  }) {
    Color defaultColor;
    switch (type) {
      case TextStyleType.heading:
        defaultColor = context.headingTextColor;
        break;
      case TextStyleType.subHeading:
        defaultColor = context.subHeadingTextColor;
        break;
      case TextStyleType.body:
        defaultColor = context.bodyTextColor;
        break;
      case TextStyleType.label:
        defaultColor = context.hintTextColor;
        break;
    }

    return TextStyle(
      color: color ?? defaultColor,
      fontWeight: fontWeight,
      fontSize: fontSize,
    );
  }

  /// Wrap a widget with theme-aware BlocBuilder
  static Widget wrapWithThemeBuilder({
    required Widget child,
    required BuildContext context,
  }) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) => child,
    );
  }

  /// Get theme-aware scaffold background color
  static Color getScaffoldBackgroundColor(BuildContext context) {
    return context.backgroundColor;
  }

  /// Get theme-aware app bar theme
  static AppBarTheme getAppBarTheme(BuildContext context) {
    return AppBarTheme(
      backgroundColor: context.appColors.appBarBackground,
      foregroundColor: context.primaryForegroundColor,
      elevation: 0,
      iconTheme: IconThemeData(
        color: context.primaryForegroundColor,
      ),
      titleTextStyle: TextStyle(
        color: context.primaryForegroundColor,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// Get theme-aware dialog decoration
  static BoxDecoration getDialogDecoration(BuildContext context) {
    return BoxDecoration(
      color: context.dialogBackgroundColor,
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: [
        BoxShadow(
          color: context.appColors.shadow,
          offset: const Offset(0, 4),
          blurRadius: 8,
        ),
      ],
    );
  }

  /// Get theme-aware input decoration
  static InputDecoration getInputDecoration(
    BuildContext context, {
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool enabled = true,
  }) {
    return InputDecoration(
      hintText: hintText,
      labelText: labelText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      enabled: enabled,
      filled: true,
      fillColor: context.inputFieldBackgroundColor,
      hintStyle: TextStyle(color: context.hintTextColor),
      labelStyle: TextStyle(color: context.bodyTextColor),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: context.inputFieldBorderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: context.inputFieldBorderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide:
            BorderSide(color: context.inputFieldFocusedBorderColor, width: 2.0),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: context.disabledColor),
      ),
    );
  }

  /// Get theme-aware elevated button style
  static ButtonStyle getElevatedButtonStyle(
    BuildContext context, {
    Color? backgroundColor,
    Color? foregroundColor,
    double borderRadius = 8.0,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? context.primaryDark,
      foregroundColor: foregroundColor ?? context.primaryForegroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      elevation: 2,
    );
  }

  /// Get theme-aware outlined button style
  static ButtonStyle getOutlinedButtonStyle(
    BuildContext context, {
    Color? borderColor,
    Color? foregroundColor,
    double borderRadius = 8.0,
  }) {
    return OutlinedButton.styleFrom(
      foregroundColor: foregroundColor ?? context.primaryDark,
      side: BorderSide(color: borderColor ?? context.primaryDark),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }

  /// Get theme-aware text button style
  static ButtonStyle getTextButtonStyle(
    BuildContext context, {
    Color? foregroundColor,
  }) {
    return TextButton.styleFrom(
      foregroundColor: foregroundColor ?? context.primaryDark,
    );
  }

  /// Get theme-aware alert dialog theme
  static AlertDialog getAlertDialog(
    BuildContext context, {
    String? title,
    String? content,
    List<Widget>? actions,
  }) {
    return AlertDialog(
      backgroundColor: context.dialogBackgroundColor,
      title: title != null
          ? Text(
              title,
              style: TextStyle(color: context.headingTextColor),
            )
          : null,
      content: content != null
          ? Text(
              content,
              style: TextStyle(color: context.bodyTextColor),
            )
          : null,
      actions: actions,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
    );
  }
}

/// Enum for different text style types
enum TextStyleType {
  heading,
  subHeading,
  body,
  label,
}
