import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/main.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:get/get.dart';
// Corrected import for the floating search bar. The correct package is material_floating_search_bar.
import 'package:material_floating_search_bar_2/material_floating_search_bar_2.dart';

import 'job_details_page.dart';

// --- Data Models (Placeholders) ---
class ListElement {
  final String id;
  final String? name;
  final String? description;
  final String? location;
  final String? skillNames;
  final String? organizedBy;
  final String? domainName;
  final String? image;
  final String? experience;
  final double? minExperience;
  final double? maxExperience;
  final int? jobStatusNumeric;
  final RxBool isApplied;

  ListElement({
    required this.id,
    this.name,
    this.description,
    this.location,
    this.skillNames,
    this.organizedBy,
    this.domainName,
    this.image,
    this.experience,
    this.minExperience,
    this.maxExperience,
    this.jobStatusNumeric,
    bool isApplied = false,
  }) : isApplied = isApplied.obs;
}

// --- Dummy ApiService for demonstration ---
class _ApiService {
  Future<List<ListElement>> fetchJobs(String? query,
      {String? jobRolesId, String? domainId}) async {
    await Future.delayed(const Duration(seconds: 1));

    if (query?.toLowerCase() == 'error') {
      throw 'Failed to fetch jobs. Please check your connection.';
    }

    final List<ListElement> allJobs = [
      ListElement(
          id: '1',
          name: 'Senior Flutter Developer',
          organizedBy: 'Singularis',
          location: 'Remote',
          minExperience: 3,
          maxExperience: 5,
          image: 'https://via.placeholder.com/150/0000FF/FFFFFF?Text=S',
          jobStatusNumeric: 0),
      ListElement(
          id: '2',
          name: 'UI/UX Designer',
          organizedBy: 'MecFuture',
          location: 'New York, NY',
          minExperience: 2,
          maxExperience: 4,
          image: 'https://via.placeholder.com/150/FF0000/FFFFFF?Text=M',
          jobStatusNumeric: 1,
          isApplied: true),
      ListElement(
          id: '3',
          name: 'Backend Go Engineer',
          organizedBy: 'Google',
          location: 'Mountain View, CA',
          minExperience: 4,
          maxExperience: 7,
          image: 'https://via.placeholder.com/150/00FF00/FFFFFF?Text=G',
          jobStatusNumeric: 0),
      ListElement(
          id: '4',
          name: 'Product Manager',
          organizedBy: 'Facebook',
          location: 'Menlo Park, CA',
          minExperience: 5,
          maxExperience: 10,
          image: 'https://via.placeholder.com/150/FFFF00/000000?Text=F',
          jobStatusNumeric: 0),
    ];

    if (query?.toLowerCase() == 'empty') {
      return [];
    }

    if (jobRolesId != null || domainId != null) {
      return allJobs.where((job) => job.id == '1' || job.id == '3').toList();
    }

    if (query == null || query.isEmpty) {
      return allJobs;
    }

    return allJobs
        .where((job) =>
            (job.name?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
            (job.organizedBy?.toLowerCase().contains(query.toLowerCase()) ??
                false))
        .toList();
  }

  Future<void> applyForJob(String jobId) async {
    await Future.delayed(const Duration(seconds: 1));
    debugPrint('Successfully applied for job $jobId');
  }
}

// --- GetX Controller with StateMixin for simplified state management ---
class JobSearchController extends GetxController
    with StateMixin<List<ListElement>> {
  final _apiService = _ApiService();
  final String? jobRolesId;
  final String? domainId;

  JobSearchController({this.jobRolesId, this.domainId});

  final searchBarController = FloatingSearchBarController();

  @override
  void onInit() {
    super.onInit();
    fetchJobs(initialSearch: true);
  }

  Future<void> fetchJobs({bool initialSearch = false}) async {
    // Set state to loading
    change(null, status: RxStatus.loading());
    try {
      final query = searchBarController.query;
      final result = await _apiService.fetchJobs(
        query,
        jobRolesId: initialSearch ? jobRolesId : null,
        domainId: initialSearch ? domainId : null,
      );

      if (result.isEmpty) {
        // Set state to empty
        change(null, status: RxStatus.empty());
      } else {
        // Set state to success and pass the data
        change(result, status: RxStatus.success());
      }
    } catch (e) {
      // Set state to error
      change(null, status: RxStatus.error(e.toString()));
    }
  }

  Future<void> applyForJob(ListElement job) async {
    if (job.isApplied.value) return;
    job.isApplied.value = true;

    try {
      await _apiService.applyForJob(job.id);
      Get.snackbar('Success', 'Application for ${job.name} submitted!',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: navigatorKey.currentContext?.appColors.primaryForeground);
    } catch (e) {
      job.isApplied.value = false;
      Get.snackbar('Error', 'Failed to apply. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: navigatorKey.currentContext?.appColors.error,
          colorText: navigatorKey.currentContext?.appColors.primaryForeground);
    }
  }

  void refreshJobs() {
    fetchJobs();
  }
}

// --- UI ---
class JobSearchViewPage extends GetView<JobSearchController> {
  final String? appBarTitle;
  // Restored parameters to fix constructor errors in job_dashboard_page.dart
  final String? jobRolesId;
  final String? domainId;

  const JobSearchViewPage({
    super.key,
    this.appBarTitle,
    this.jobRolesId,
    this.domainId,
  });

  @override
  Widget build(BuildContext context) {
    // Initialize the controller with filter IDs
    Get.put(JobSearchController(jobRolesId: jobRolesId, domainId: domainId));

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: FloatingSearchBar(
        controller: controller.searchBarController,
        hint: tr('search_skills'),
        scrollPadding: const EdgeInsets.only(top: 16, bottom: 56),
        transitionDuration: const Duration(milliseconds: 800),
        transitionCurve: Curves.easeInOut,
        physics: const BouncingScrollPhysics(),
        axisAlignment: 0.0,
        openAxisAlignment: 0.0,
        width: Get.width * 0.9,
        debounceDelay: const Duration(milliseconds: 500),
        onSubmitted: (query) {
          controller.fetchJobs();
          controller.searchBarController.close();
        },
        transition: CircularFloatingSearchBarTransition(),
        actions: [
          FloatingSearchBarAction.searchToClear(showIfClosed: false),
        ],
        builder: (context, transition) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Material(
              color: context.appColors.primaryForeground,
              elevation: 4.0,
              child: Container(height: 0),
            ),
          );
        },
        body: Column(
          children: [
            const SizedBox(height: 70),
            Expanded(
              // controller.obx now works because the controller uses StateMixin
              child: controller.obx(
                (state) => ListView.builder(
                  // state is the job list from the controller
                  itemCount: state?.length ?? 0,
                  itemBuilder: (context, index) {
                    final job = state![index];
                    return _JobListItem(
                      job: job,
                      onApply: () => controller.applyForJob(job),
                      onTap: () {
                        Get.to(() => JobDetailsPage(
                              title: job.name,
                              description: job.description,
                              location: job.location,
                              skillNames: job.skillNames,
                              companyName: job.organizedBy,
                              domain: job.domainName,
                              companyThumbnail: job.image,
                              experience: job.experience,
                              id: int.tryParse(job.id),
                              jobStatusNumeric: job.jobStatusNumeric,
                            ))?.then((_) => controller.refreshJobs());
                      },
                    );
                  },
                ),
                onLoading: const Center(child: CircularProgressIndicator()),
                onEmpty: Center(child: Text(tr('no_jobs_found'))),
                onError: (error) =>
                    Center(child: Text(error ?? tr('unknown_error'))),
              ),
            ),
          ],
        ),
        leadingActions: [
          FloatingSearchBarAction.back(showIfClosed: true),
        ],
        title: Text(
          appBarTitle ?? tr('find_jobs'),
          style: Theme.of(context).textTheme.titleLarge,
        ),
      ),
    );
  }
}

class _JobListItem extends StatelessWidget {
  final ListElement job;
  final VoidCallback onApply;
  final VoidCallback onTap;

  const _JobListItem({
    required this.job,
    required this.onApply,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: onTap,
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
        elevation: 2.0,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: Image.network(
                  job.image ?? 'https://via.placeholder.com/150',
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stacktrace) =>
                      const Icon(Icons.business, size: 60),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(job.name ?? 'N/A',
                        style: textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Text(job.organizedBy ?? 'N/A', style: textTheme.bodyMedium),
                    const SizedBox(height: 8),
                    if (job.minExperience != null || job.maxExperience != null)
                      _buildIconText(context,
                          icon: Icons.work_outline,
                          text:
                              '${tr('exp')}: ${job.minExperience ?? 0}-${job.maxExperience ?? 0} ${tr('yrs')}'),
                    const SizedBox(height: 4),
                    if (job.location != null && job.location!.isNotEmpty)
                      _buildIconText(context,
                          icon: Icons.location_on_outlined,
                          text: job.location!),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Obx(
                () => TextButton(
                  onPressed: job.isApplied.value ? null : onApply,
                  child: Text(
                    job.isApplied.value ? tr('applied') : tr('apply_buttn'),
                    style: TextStyle(
                        color: job.isApplied.value
                            ? Colors.grey
                            : colorScheme.primary,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIconText(BuildContext context,
      {required IconData icon, required String text}) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 6),
        Flexible(
          child: Text(text,
              style: Theme.of(context).textTheme.bodySmall,
              overflow: TextOverflow.ellipsis),
        ),
      ],
    );
  }
}
