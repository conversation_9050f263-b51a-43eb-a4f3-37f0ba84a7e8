import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/pages/gcarvaan/createpost/create_gcarvaan_page.dart';
import 'package:masterg/pages/gcarvaan/createpost/create_post_provider.dart';
import 'package:masterg/pages/reels/trim_video/trimmer_view.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';

class VideoRecordingCameraPage extends StatefulWidget {
  final CreatePostProvider? provider;
  final Function(String? path, bool isRecorded)? onRecordFinish;
  final bool? runOnRecordFinish;
  final bool isFrontCamera;
  final bool infiniteRecording;

  ///Record duration in seconds
  final int? recordDuration;
  final bool? enableQualityDowngrade;
  final bool? hideFilePicker;

  const VideoRecordingCameraPage(
      {super.key,
      this.provider,
      this.onRecordFinish,
      this.runOnRecordFinish = false,
      this.isFrontCamera = false,
      this.infiniteRecording = false,
      this.enableQualityDowngrade = false,
      this.recordDuration,
      this.hideFilePicker = false});

  @override
  State<VideoRecordingCameraPage> createState() =>
      _VideoRecordingCameraPageState();
}

class _VideoRecordingCameraPageState extends State<VideoRecordingCameraPage>
    with WidgetsBindingObserver {
  bool _isLoading = true;
  bool _isRecording = false;
  late CameraController _cameraController;
  bool isFront = false;
  bool flashOn = false;
  late Timer timer;
  int recordingDuration = 15;
  late int secondsRemaining = recordingDuration;
  int runningSeconds = 0;

  bool pickingFile = false;

  //voice code

  final SpeechToText _speechToText = SpeechToText();
  bool speechEnabled = false;
  String lastWords = '';

  Timer? commandExecutionTimer;
  final Duration commandExecutionDelay = Duration(seconds: 2);
  int countCall = 0;

  /// Each time to start a speech recognition session
  void startListening() async {
    log("rec cmd start");
    speechEnabled = true;
    try {
      await _speechToText.listen(
          listenFor: Duration(minutes: 10),
          onResult: (SpeechRecognitionResult result) {
            log("rec cmd word: ${result.recognizedWords}");
          });
    } catch (e) {
      log("rec cmd something went wrong $e");
    }
    // setState(() {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      log("App resumed - screen is not locked");
    } else {
      if (_isRecording) _recordVideo();
      log("App is in a different state - screen may be locked");
    }
  }

  void stopListening() async {
    await _speechToText.stop();
    speechEnabled = false;
    setState(() {});
  }

  // void _initSpeech() async {
  //   try {
  //    log('something wrong start');
  //     await _speechToText.initialize();
  //   } catch (e) {
  //    log('something wrong $e');
  //   }

  //   setState(() {});
  // }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    recordingDuration = widget.recordDuration ?? recordingDuration;
    secondsRemaining = recordingDuration;
    isFront = widget.isFrontCamera;
    _initCamera(isFront);

    // _initSpeech();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cameraController.dispose();
    removeTimer();
    super.dispose();
  }

  void removeTimer() {
    setState(() {
      timer.cancel();
    });
  }

  Future<void> _initCamera(bool isfront) async {
    log('recorder value is ${widget.runOnRecordFinish}');
    final cameras = await availableCameras();
    ResolutionPreset quality = ResolutionPreset.max;

    final front = cameras.firstWhere((camera) => isfront
        ? camera.lensDirection == CameraLensDirection.front
        : camera.lensDirection == CameraLensDirection.back);

    if (widget.enableQualityDowngrade == true) {
      if (Platform.isIOS) {
        quality = ResolutionPreset.low;
      } else {
        quality =
            isfront == true ? ResolutionPreset.medium : ResolutionPreset.low;
      }
    }
    _cameraController = CameraController(front, quality, enableAudio: true);
    await _cameraController.initialize();
    _cameraController.addListener(() {
      if (_cameraController.value.isRecordingVideo) {
        log("recording video");
      } else {
        log("recording video paused");
        removeTimer();
        _recordVideo();
      }
    });
    setState(() => _isLoading = false);
  }

  Future<void> _recordVideo() async {
    log('return path 1');

    if (pickingFile) return;
    if (_isRecording) {
      final file = await _cameraController.stopVideoRecording();
      //setState(() => _isRecording = false);
      setState(() {
        _isRecording = false;
      });

      if (widget.runOnRecordFinish == true) {
        try {
          widget.onRecordFinish!(file.path, true);
        } catch (e) {}
        return Navigator.of(context).pop(false);
      }
      timer.cancel();

      widget.provider?.addToList(file.path);
      // stopListening();

      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => CreateGCarvaanPage(
                    isReelsPost: true,
                    fileToUpload: [],
                    filesPath: widget.provider?.files,
                    provider: widget.provider,
                  )));
    } else {
      // startListening();
// return;
      await _cameraController.prepareForVideoRecording();
      secondsRemaining = recordingDuration;
      await _cameraController.startVideoRecording();

      if (widget.infiniteRecording == false) {
        timer = Timer.periodic(Duration(seconds: 1), (_) async {
          if (secondsRemaining != 0) {
            setState(() {
              secondsRemaining--;
            });
          } else {
            timer.cancel();
            final file = await _cameraController.stopVideoRecording();
            setState(() {
              _isRecording = false;
              secondsRemaining = 0;
            });

            if (widget.runOnRecordFinish == true) {
              widget.onRecordFinish!(file.path, true);
              return Navigator.of(context).pop(false);
            }
            widget.provider?.addToList(file.path);
            await Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => CreateGCarvaanPage(
                          isReelsPost: true,
                          fileToUpload: [],
                          filesPath: widget.provider?.files,
                          provider: widget.provider,
                        )));
          }
        });
      } else {
        //set timer only
        timer = Timer.periodic(Duration(seconds: 1), (timer) {
          setState(() {
            runningSeconds += 1;
          });
        });
      }

      setState(() => _isRecording = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        color: context.appColors.surface,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    } else {
      return Scaffold(
        backgroundColor: Colors.black,
        bottomSheet: Container(
          height: 70,
          width: MediaQuery.of(context).size.width,
          color: context.appColors.textBlack,
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: pickingFile
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'validate_file',
                      style: Styles.semibold(
                          color: context.appColors.primaryForeground),
                    ).tr(),
                    SizedBox(width: 10),
                    SizedBox(
                        width: 15,
                        height: 15,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: context.appColors.primaryForeground,
                        )),
                  ],
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                      widget.hideFilePicker == false && !_isRecording
                          ? InkWell(
                              onTap: () async {
                                try {
                                  widget.provider?.clearList();
                                  setState(() {
                                    pickingFile = true;
                                  });
                                  _initFilePiker(widget.provider, true);
                                } catch (e, stacktrace) {
                                  log('exception is $stacktrace');
                                } finally {}
                              },
                              child: Stack(
                                children: [
                                  Container(
                                    height: 40,
                                    width: 40,
                                    margin: const EdgeInsets.only(left: 20),
                                    decoration: BoxDecoration(
                                      color: context.appColors.surface,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: context.appColors.blueButton,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Icon(Icons.add,
                                          color: context
                                              .appColors.primaryForeground),
                                    ),
                                  )
                                ],
                              ),
                            )
                          : Spacer(),
                      if (!_isRecording)
                        Container(
                            height: 40,
                            width: 40,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: context.appColors.grey2,
                            ),
                            margin: const EdgeInsets.only(
                              right: 20,
                            ),
                            child: IconButton(
                                onPressed: () async {
                                  setState(() {
                                    isFront = !isFront;
                                    _isLoading = true;
                                  });
                                  _initCamera(isFront);
                                },
                                icon: Icon(
                                  Icons.flip_camera_android,
                                  color: context.appColors.textWhite,
                                ))),
                    ]),
        ),
        body: Center(
          child: Listener(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                CameraPreview(
                  _cameraController,
                  child: Stack(
                    // alignment: Alignment.topCenter,
                    children: [
                      /*ClipRRect(
                        borderRadius: BorderRadius.circular(1),
                        child: Container(
                          height: 6,
                          child: LinearProgressIndicator(
                            value: double.tryParse('0.'+progress),// percent filled
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                            backgroundColor: Color(0xFFFFDAB8),
                          ),
                        ),
                      ),*/
                      /*Container(
                        height: 6,
                        width: MediaQuery.of(context).size.width -
                            (MediaQuery.of(context).size.width *
                                (secondsRemaining / 15)),
                        decoration: BoxDecoration(
                            color: context.appColors.orange,
                            borderRadius: BorderRadius.circular(10)),
                      ),*/

                      Padding(
                        padding: const EdgeInsets.only(top: 15.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.close,
                                color: context.appColors.primaryForeground,
                              ),
                              tooltip: 'Close Camera',
                              onPressed: () {
                                if (pickingFile) return;
                                Navigator.of(context).pop(false);
                              },
                            ),
                            if (!widget.infiniteRecording) ...[
                              _isRecording
                                  ? Text(
                                      Utility.showDuration(secondsRemaining),
                                      style: Styles.bold(
                                          color: secondsRemaining < 120 &&
                                                  widget.recordDuration != null
                                              ? context.appColors.error
                                              : context.appColors.textWhite),
                                    )
                                  : GestureDetector(
                                      child: Row(children: [
                                        Text(
                                          Utility.showDuration(
                                              secondsRemaining),
                                          style: Styles.bold(
                                              color: secondsRemaining < 60 &&
                                                      widget.recordDuration !=
                                                          null
                                                  ? context.appColors.error
                                                  : context
                                                      .appColors.textWhite),
                                        ),
                                        if (widget.recordDuration == null)
                                          Icon(
                                            Icons.arrow_drop_down_sharp,
                                            color: context.appColors.textWhite,
                                          )
                                      ]),
                                      onTapDown: (TapDownDetails detail) {
                                        showDurationDropdown(
                                            context, detail.globalPosition);
                                      },
                                    ),
                            ] else ...[
                              Text(
                                Utility.showDuration(runningSeconds),
                                style: Styles.bold(
                                    color: secondsRemaining < 60 &&
                                            widget.recordDuration != null
                                        ? context.appColors.error
                                        : context.appColors.textWhite),
                              )
                            ],
                            IconButton(
                              icon: Icon(
                                flashOn == true
                                    ? Icons.flash_on_sharp
                                    : Icons.flash_off_rounded,
                                color: context.appColors.textWhite,
                              ),
                              tooltip: 'Flash',
                              onPressed: () {
                                if (pickingFile) return;

                                setState(() {
                                  if (flashOn) {
                                    _cameraController
                                        .setFlashMode(FlashMode.off);
                                    flashOn = false;
                                  } else {
                                    _cameraController
                                        .setFlashMode(FlashMode.torch);
                                    flashOn = true;
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                Padding(
                  //padding: const EdgeInsets.all(10),
                  padding: const EdgeInsets.only(bottom: 40.0),
                  child: FloatingActionButton(
                    backgroundColor: context.appColors.surface,
                    child: _isRecording
                        ? SvgPicture.asset(
                            'assets/images/GReelsS.svg',
                            height: 40,
                            width: 40,
                            allowDrawingOutsideViewBox: true,
                          )
                        : SvgPicture.asset(
                            'assets/images/GReels.svg',
                            height: 40,
                            width: 40,
                            allowDrawingOutsideViewBox: true,
                          ),
                    // child: Icon(_isRecording ? Icons.stop : Icons.circle),
                    onPressed: () => _recordVideo(),
                  ),
                ),
              ],
            ),
          ),

          /*child: Listener(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                CameraPreview(_cameraController,
                  child: Stack(
                    alignment: Alignment.topLeft,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.close, color: context.appColors.primaryForeground,),
                        tooltip: 'Close Camera',
                        onPressed: () {
                          Navigator.of(context).pop(false);
                        },
                      ),
                    ],
                  ),),
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: FloatingActionButton(
                    backgroundColor: Colors.error,
                    child: Icon(_isRecording ? Icons.stop : Icons.circle),
                    onPressed: () => _recordVideo(),
                  ),
                ),
              ],
            ),
          ),*/
        ),
      );
    }
  }

  void showDurationDropdown(BuildContext context, Offset offset) {
    final items = [
      Duration(seconds: 15),
      Duration(seconds: 30),
    ];

    final popupMenuItems = items.map((duration) {
      return PopupMenuItem(
        value: duration.inSeconds,
        child: Text('${duration.inSeconds} sec'),
      );
    }).toList();

    final screenSize = MediaQuery.of(context).size;
    double left = offset.dx;
    double top = offset.dy;
    double right = screenSize.width - offset.dx;
    double bottom = screenSize.height - offset.dy;

    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(left, top, right, bottom),
      items: popupMenuItems,
    ).then((value) {
      if (value != null) {
        setState(() {
          secondsRemaining = value;
          recordingDuration = value;
        });
      }
    });
  }

  Future<void> _initFilePiker(CreatePostProvider? provider, isVideo) async {
    FilePickerResult? result;

    // if (await Permission.storage.request().isGranted) {
    if (Platform.isIOS) {
      result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          type: FileType.video,
          withData: false,
          allowedExtensions: null);
    } else {
      result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          type: FileType.custom,
          withData: false,
          onFileLoading: (path) {},
          lockParentWindow: true,
          allowedExtensions: ['mp4', 'mov']);
    }

    if (result != null) {
      if (!['mp4', 'mov']
          .contains(Utility.fileExtension(filePath: '${result.paths.first}'))) {
        // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        //   content: Text('only_video_allowed_upload').tr(),
        // ));

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${tr('upload_only')} ${[
          'mp4',
          'mov'
        ].toList().join(', ')} ')));
        setState(() {
          pickingFile = false;
        });
        return;
      }
      if (widget.runOnRecordFinish == true) {
        if (File(result.paths.first!).lengthSync() > 225 * 1024 * 1024) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text("${tr('content_file_size_larger_than')}  200MB"),
          ));
          setState(() {
            pickingFile = false;
          });
          return;
        }
        try {
          setState(() {
            pickingFile = false;
          });
          widget.onRecordFinish!(result.paths.first, false);
        } catch (e) {
          log('return path $e');
        }
        return Navigator.of(context).pop(false);
      }
      if (File(result.paths.first!).lengthSync() > 100 * 1024 * 1024) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("${tr('content_file_size_larger_than')}  100MB"),
        ));
      } else if (result.paths.first!.contains('.mp4') ||
          result.paths.first!.contains('.mov')) {
        provider?.addToList(result.paths.first);

        await Navigator.of(context).push(
          MaterialPageRoute(builder: (context) {
            return TrimmerView(File('${provider?.files?.first}'),
                provider: provider!);
          }),
        );
        if (provider?.files?.length != 0) {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => CreateGCarvaanPage(
                        isReelsPost: true,
                        fileToUpload: [],
                        filesPath: provider?.files,
                        provider: provider,
                      )));
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('only_video_allowed_upload').tr(),
        ));
      }
    }

    setState(() {
      pickingFile = false;
    });
    // }
  }
}
