import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:masterg/data/providers/mg_assessment_detail_provioder.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/tap_widget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/certificate_container.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/training_pages/assessment_report_screen.dart';
import 'package:masterg/pages/training_pages/new_screen/assessment_attempt_page.dart';
import 'package:masterg/pages/training_pages/new_screen/assessment_review_page.dart';
import 'package:masterg/utils/strings.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/custom_progress_indicator.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;
import '../singularis/competition/assessment_certificate.dart';

class MgAssessmentDetailPage extends StatefulWidget {
  BuildContext? mContext;
  final String? programName;
  final bool? isAssessmentReport;

  MgAssessmentDetailPage(
      {super.key, required this.programName, this.isAssessmentReport});

  @override
  State<MgAssessmentDetailPage> createState() => _MgAssessmentDetailPageState();
}

class _MgAssessmentDetailPageState extends State<MgAssessmentDetailPage> {
  late final TextEditingController pinController;

  @override
  void initState() {
    super.initState();
    pinController = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MgAssessmentDetailProvider>(
        builder: (context, assessmentDetailProvider, child) {
      return Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: context.appColors.background,
          body: Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Stack(children: [
              assessmentDetailProvider.assessmentResponse?.data?.instruction !=
                      null
                  ? _buildBody(context, assessmentDetailProvider)
                  : CustomProgressIndicator(true, context.appColors.surface),
              assessmentDetailProvider.assessmentResponse?.data?.instruction !=
                      null
                  ? assessmentDetailProvider.assessmentResponse!.data!
                              .instruction!.details!.isCertificate !=
                          0
                      ? Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: InkWell(
                            onTap: () {
                              /* Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => CertificateView(
                                certificateId: assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!
                                    .certificate!,
                                programName: programName!,
                                contentId: int.parse(assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!.contentId.toString()))));*/
                              Navigator.of(context).push(MaterialPageRoute(
                                  builder: (context) =>
                                      EventAssessmentCertificate(
                                        contentId: int.tryParse(
                                            assessmentDetailProvider
                                                    .assessmentResponse!
                                                    .data!
                                                    .instruction!
                                                    .details!
                                                    .contentId ??
                                                ''),
                                        certificateId: assessmentDetailProvider
                                            .assessmentResponse!
                                            .data!
                                            .instruction!
                                            .details!
                                            .certificateId,
                                        htmlUrl: assessmentDetailProvider
                                            .assessmentResponse!
                                            .data!
                                            .instruction!
                                            .details!
                                            .certificateHtmlUrl,
                                      )));
                            },
                            child: CertificateContainer(),
                          ),
                        )
                      : SizedBox()
                  : SizedBox()
            ]),
          ));
    });
  }

  Container _buildBody(BuildContext context,
      MgAssessmentDetailProvider assessmentDetailProvider) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      margin: assessmentDetailProvider.assessmentResponse!.data!.instruction!
                  .details!.isCertificate !=
              0
          ? EdgeInsets.only(bottom: 40)
          : EdgeInsets.only(bottom: 0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Row(
              children: [
                TapWidget(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: context.appColors.surface,
                        borderRadius: BorderRadius.all(Radius.circular(10))),
                    child: Icon(
                      Icons.arrow_back_ios_rounded,
                      color: context.appColors.textBlack,
                      size: 20,
                    ),
                  ),
                ),
                // SizedBox(width: 80),
                Expanded(
                  child: Text(
                    '${assessmentDetailProvider.assessments.title}',
                    style: Styles.getBoldThemeStyle(context, size: 18),
                    textAlign: TextAlign.center,
                  ),
                )
              ],
            ),
            _belowTitle(assessmentDetailProvider, context),
            _body(context, assessmentDetailProvider, currentIndiaTime!),
          ],
        ),
      ),
    );
  }

  Padding _belowTitle(MgAssessmentDetailProvider assessmentDetailProvider,
      BuildContext context) {
    int attempLeft = assessmentDetailProvider
            .assessmentResponse!.data!.instruction!.details!.attemptAllowed! -
        assessmentDetailProvider
            .assessmentResponse!.data!.instruction!.details!.attemptCount!;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _size(height: 20),
          Row(
            children: [
              Text('${tr('submit_before')}: '),
              Text(
                assessmentDetailProvider.assessments.endDate != null
                    ? Utility.convertDateFromMillis(
                        assessmentDetailProvider.assessments.endDate!,
                        Strings.REQUIRED_DATE_DD_MMM_YYYY)
                    : '',
                style: Styles.semibold(
                    size: 14, color: context.appColors.textBlack),
                textDirection: ui.TextDirection.ltr,
              ),
              Spacer(),
              Text(
                '${assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.durationInMinutes} ${tr('mins')}',
                style: Styles.semibold(
                    size: 14, color: context.appColors.textBlack),
              ),
            ],
          ),
          _size(height: 15),
          Text(
            assessmentDetailProvider.assessments.title!,
            style:
                Styles.bold(size: 16, color: context.appColors.gradientRight),
          ),
          _size(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                '${tr('marks')} ${assessmentDetailProvider.assessments.maximumMarks}  • ',
                style: Styles.semibold(
                    size: 14, color: context.appColors.textBlack),
              ),
              Text(
                assessmentDetailProvider.assessmentResponse!.data!.instruction!
                            .details!.attemptAllowed ==
                        0
                    ? tr('unlimited_attempt')
                    : ' $attempLeft ${tr('attempt_available')}',
                style: Styles.semibold(
                    size: 14, color: context.appColors.textBlack),
              )
            ],
          ),
        ],
      ),
    );
  }

  SizedBox _size({double height = 20}) {
    return SizedBox(
      height: height,
    );
  }

  final defaultPinTheme = PinTheme(
    width: 45,
    height: 45,
    textStyle: TextStyle(
        fontSize: 20, color: Color(0xff263367), fontWeight: FontWeight.w600),
    decoration: BoxDecoration(
      border: Border.all(color: Color(0xFFEAEFF3)),
      borderRadius: BorderRadius.circular(10),
    ),
  );

  void _showMyDialog(BuildContext rootContext, String? passcode,
      MgAssessmentDetailProvider assessmentDetailProvider) {
    const focusedBorderColor = Color(0xFF17AB90);
    const fillColor = Color(0x00F3F6F9);
    const borderColor = Color(0x6617AB90);
    final defaultPinTheme = PinTheme(
      width: 45,
      height: 45,
      textStyle: const TextStyle(
        fontSize: 20,
        color: Color(0xFF1E3C57),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: borderColor),
      ),
    );

    showDialog(
      context: rootContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        pinController.clear();
        return AlertDialog(
          title: Text(tr('enter_your_passcode')),
          icon: Icon(
            Icons.password,
            color: context.appColors.primaryDark,
          ),
          content: Text(
            tr('passcode_msg_popup'),
            textAlign: TextAlign.center,
          ),
          actions: <Widget>[
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Pinput(
                  controller: pinController,
                  //focusNode: focusNode,
                  length: 6,
                  defaultPinTheme: defaultPinTheme,
                  //separatorBuilder: (index) => const SizedBox(width: 8),
                  validator: (value) {
                    return value == passcode
                        ? null
                        : tr('enter_valid_passcode');
                  },
                  hapticFeedbackType: HapticFeedbackType.lightImpact,
                  onCompleted: (pin) {
                    debugPrint('onCompleted: $pin');
                  },
                  onChanged: (value) {
                    debugPrint('onChanged: $value');
                  },
                  cursor: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 9),
                        width: 22,
                        height: 1,
                        color: focusedBorderColor,
                      ),
                    ],
                  ),
                  focusedPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: focusedBorderColor),
                    ),
                  ),
                  submittedPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      color: fillColor,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: focusedBorderColor),
                    ),
                  ),
                  errorPinTheme: defaultPinTheme.copyBorderWith(
                    border: Border.all(color: Colors.redAccent),
                  ),
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  child: Text('Cancel'),
                  onPressed: () {
                    pinController.clear();
                    Navigator.of(context).pop(); // Closes the dialog
                  },
                ),
                TextButton(
                  child: Text('OK'),
                  onPressed: () async {
                    // Add any action here
                    if (pinController.text.length == 6) {
                      if (passcode == pinController.text) {
                        await Navigator.push(
                            context,
                            NextPageRoute(
                              AssessmentAttemptPage(
                                  contentId: assessmentDetailProvider
                                      .assessments.contentId,
                                  isVideoTypeQuiz: assessmentDetailProvider
                                          .assessmentResponse!
                                          .data!
                                          .instruction!
                                          .details!
                                          .quizType !=
                                      'text',
                                  programId: assessmentDetailProvider
                                      .assessments.program),
                            ));
                        assessmentDetailProvider.getDetails();

                        pinController.clear();
                        Navigator.of(context).pop();
                      } else {
                        /*ScaffoldMessenger.of(rootContext).showSnackBar(
                            SnackBar(
                              content: Text(tr('enter_valid_otp')),
                              duration: Duration(seconds: 5), // Set to a reasonable duration
                              action: SnackBarAction(
                                label: 'Dismiss', // Label for the action
                                onPressed: () {
                                  // Dismiss the SnackBar when the user presses the button
                                  ScaffoldMessenger.of(rootContext).hideCurrentSnackBar();
                                },
                              ),
                            ),
                          );*/
                      }
                    } else {
                      // Show a new SnackBar
                      /*ScaffoldMessenger.of(rootContext).showSnackBar(
                          SnackBar(
                            content: Text(tr('enter_otp')),
                            duration: Duration(seconds: 5), // Set to a reasonable duration
                            action: SnackBarAction(
                              label: 'Dismiss', // Label for the action
                              onPressed: () {
                                // Dismiss the SnackBar when the user presses the button
                                ScaffoldMessenger.of(rootContext).hideCurrentSnackBar();
                              },
                            ),
                          ),
                        );*/
                    }
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Container _body(BuildContext context,
      MgAssessmentDetailProvider assessmentDetailProvider, DateTime value) {
    bool isReviewAllow = false;

    if (assessmentDetailProvider
            .assessmentResponse!.data!.instruction!.details!.isReviewAllowed ==
        0) {
      isReviewAllow = false;
    } else if (assessmentDetailProvider.assessmentResponse!.data!.instruction!
                .details!.attemptAllowed ==
            0 &&
        assessmentDetailProvider
                .assessmentResponse!.data!.instruction!.details!.attemptCount! >
            0) {
      isReviewAllow = true;
    } else if (assessmentDetailProvider.assessmentResponse!.data!.instruction!
                .details!.attemptAllowed !=
            0 &&
        assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!
                .attemptCount! ==
            assessmentDetailProvider.assessmentResponse!.data!.instruction!
                .details!.attemptAllowed) {
      isReviewAllow = true;
    }
    if (assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.isReviewAllowed == 1 &&
        assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!
                .attemptAllowed! ==
            0 &&
        assessmentDetailProvider
                .assessmentResponse!.data!.instruction!.details!.attemptCount! >
            0) {
      isReviewAllow = true;
    }

    return Container(
      margin: EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
          color: context.appColors.surface,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(25), topLeft: Radius.circular(25))),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 18, vertical: 10),
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'instructions',
                  style: Styles.textExtraBold(
                      size: 18, color: context.appColors.gradientRight),
                ).tr(),
                Wrap(
                  children: List.generate(
                      assessmentDetailProvider.assessmentResponse!.data!
                          .instruction!.statement!.length,
                      (index) => Html(
                            data: assessmentDetailProvider.assessmentResponse!
                                .data!.instruction!.statement![index],
                            style: {
                              "p": Style(
                                fontFamily: 'Nunito_Regular',
                              )
                            },
                          )),
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
            _size(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (isReviewAllow)
                  Expanded(
                    child: TapWidget(
                      onTap: () {
                        if (assessmentDetailProvider.assessmentResponse!.data!
                                .instruction!.details!.quizType ==
                            'text') {
                          Navigator.push(
                              context,
                              NextPageRoute(AssessmentReviewPage(
                                  contentId: int.tryParse(
                                      '${assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.contentId}'),
                                  programId: assessmentDetailProvider
                                      .assessments.program)));
                        } else {
                          Navigator.push(
                              context,
                              NextPageRoute(AssessmentAttemptPage(
                                contentId: assessmentDetailProvider
                                    .assessments.contentId,
                                programId: assessmentDetailProvider
                                    .assessments.program,
                                // isInterview: widget.fromJob,
                                isVideoTypeQuiz: assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .quizType !=
                                    'text',
                                isReview: true,
                              )));
                        }
                      },
                      child: Container(
                        height: 50,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: [
                            context.appColors.gradientLeft,
                            context.appColors.gradientRight,
                          ]),
                          color: context.appColors.gradientRight,
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8, right: 8, top: 4, bottom: 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'review',
                                style: Styles.textExtraBold(
                                    size: 14,
                                    color: context.appColors.primaryForeground),
                              ).tr(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                if (assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.attemptAllowed! ==
                        0
                    ? true
                    : assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.attemptCount! <=
                        assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.attemptAllowed!)
                  SizedBox(width: 15.0),
                if (assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.attemptAllowed! ==
                        0
                    ? true
                    : assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.attemptCount! <=
                        assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.attemptAllowed!)
                  Expanded(
                    child: TapWidget(
                      onTap: () async {
                        switch (Utility.classStatus(
                            assessmentDetailProvider.assessmentResponse!.data!
                                .instruction!.details!.startDate!,
                            assessmentDetailProvider.assessmentResponse!.data!
                                .instruction!.details!.endDate!,
                            value)) {
                          case 1:
                            AlertsWidget.showCustomDialog(
                                context: context,
                                title: tr('assessment_not_ready_submission'),
                                text: "",
                                icon: 'assets/images/circle_alert_fill.svg',
                                showCancel: false,
                                oKText: tr('ok'),
                                onOkClick: () async {});
                            return;

                          case 2:
                            AlertsWidget.showCustomDialog(
                                context: context,
                                title: tr('due_data_passed'),
                                text: "",
                                icon: 'assets/images/circle_alert_fill.svg',
                                showCancel: false,
                                oKText: tr('ok'),
                                onOkClick: () async {});
                            return;
                        }

                        if (assessmentDetailProvider.assessmentResponse!.data!
                                    .instruction!.details!.attemptAllowed! !=
                                0 &&
                            assessmentDetailProvider.assessmentResponse!.data!
                                    .instruction!.details!.attemptCount! >=
                                assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!
                                    .attemptAllowed!) {
                          Utility.showSnackBar(
                              scaffoldContext: context,
                              message: tr('maximum_attempts_reached'));
                        } else {
                          if (assessmentDetailProvider.assessmentResponse!.data!
                                      .instruction!.details!.isPassed ==
                                  1 &&
                              assessmentDetailProvider
                                      .assessmentResponse!
                                      .data!
                                      .instruction!
                                      .details!
                                      .allowAfterPassing ==
                                  0) {
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              content: Text('assessment_passed').tr(),
                            ));

                            return;
                          }

                          if (assessmentDetailProvider.assessmentResponse?.data
                                  ?.instruction?.details?.passcode !=
                              null) {
                            _showMyDialog(
                                context,
                                assessmentDetailProvider.assessmentResponse
                                    ?.data?.instruction?.details?.passcode,
                                assessmentDetailProvider); //For OTP
                          } else {
                            AlertsWidget.showCustomDialog(
                                context: context,
                                title: tr('confirm'),
                                text: tr('assessment_attempt_confirm'),
                                icon: 'assets/images/circle_alert_fill.svg',
                                onCancelClick: () {},
                                onOkClick: () async {
                                  await Navigator.push(
                                      context,
                                      NextPageRoute(
                                        AssessmentAttemptPage(
                                            contentId: assessmentDetailProvider
                                                .assessments.contentId,
                                            isVideoTypeQuiz:
                                                assessmentDetailProvider
                                                        .assessmentResponse!
                                                        .data!
                                                        .instruction!
                                                        .details!
                                                        .quizType !=
                                                    'text',
                                            programId: assessmentDetailProvider
                                                .assessments.program),
                                      ));
                                  assessmentDetailProvider.getDetails();
                                });
                          }
                        }
                      },
                      child: Center(
                        child: Container(
                          height: 50,
                          margin: EdgeInsets.only(right: 12),
                          decoration: BoxDecoration(
                            gradient: assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .isPassed ==
                                            1 &&
                                        assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .allowAfterPassing ==
                                            0 ||
                                    (assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .attemptCount! >=
                                            assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .attemptAllowed! &&
                                        assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .attemptAllowed !=
                                            0)
                                ? LinearGradient(colors: [
                                    context.appColors.grey3,
                                    context.appColors.grey3,
                                  ])
                                : LinearGradient(colors: [
                                    context.appColors.gradientLeft,
                                    context.appColors.gradientRight,
                                  ]),
                            color: (assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptCount! >=
                                    assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptAllowed!)
                                ? context.appColors.grey3
                                : context.appColors.gradientRight,
                            borderRadius: BorderRadius.all(Radius.circular(5)),
                          ),
                          child: Center(
                            child: Text(
                              assessmentDetailProvider.assessmentResponse!.data!
                                          .instruction!.details!.attemptCount! >
                                      0
                                  ? 'reattempt'
                                  : 'attempt',
                              style: Styles.textExtraBold(
                                  size: 14,
                                  color: context.appColors.primaryForeground),
                            ).tr(),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            _size(height: 15),
            assessmentDetailProvider.assessmentResponse!.data!.instruction!
                        .details!.showDiagnostic ==
                    1
                ? InkWell(
                    onTap: () {
                      Navigator.push(
                          context,
                          NextPageRoute(AssessmentReportScreen(
                            assessmentId: assessmentDetailProvider
                                .assessmentResponse!
                                .data!
                                .instruction!
                                .details!
                                .assessmentId,
                            assessmentDetailProvider: assessmentDetailProvider,
                            score:
                                '${assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.score ?? ''}',
                            dateTime: Utility.convertDateFromMillis(
                                assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!
                                    .submittedOnDate!,
                                Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS),
                            // '${assessmentDetailProvider.assessmentResponse?.data?.instruction?.details?.submittedOnDate != null ? Utility.convertDateFromMillis(assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.submittedOnDate!, Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS) : ''}',
                            fieldName:
                                '${assessmentDetailProvider.assessments.title}',
                            diffLevel: assessmentDetailProvider
                                    .assessments.difficultyLevel ??
                                '',
                            attemptsAllow:
                                '${assessmentDetailProvider.assessments.attemptAllowed ?? ''}',
                            attemptCount:
                                '${assessmentDetailProvider.assessments.attemptCount ?? ''}',
                            maxMarks:
                                '${assessmentDetailProvider.assessments.maximumMarks ?? ''}',
                            displayScorecard: assessmentDetailProvider
                                    .assessments.displayScorecard ??
                                0,
                          )));
                    },
                    child: Center(
                      child: Container(
                        margin: EdgeInsets.only(left: 0, right: 12),
                        height: 50,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: [
                            context.appColors.gradientLeft,
                            context.appColors.gradientRight,
                          ]),
                          color: context.appColors.gradientRight,
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8, right: 8, top: 4, bottom: 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'view_diagnostics',
                                style: Styles.textExtraBold(
                                    size: 14,
                                    color: context.appColors.primaryForeground),
                              ).tr(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  )
                : SizedBox(),
            _size(height: 10),
            if (assessmentDetailProvider.assessmentResponse!.data!.instruction!
                    .details!.submittedOnDate! !=
                0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'last_attempt',
                        style: Styles.textRegular(
                            size: 16, color: context.appColors.textBlack),
                      ).tr(),
                      Text(
                        assessmentDetailProvider.assessmentResponse!.data!
                                    .instruction!.details!.submittedOnDate !=
                                null
                            ? Utility.convertDateFromMillis(
                                assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!
                                    .submittedOnDate!,
                                Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS)
                            : '',
                        style: Styles.textRegular(
                            size: 12, color: context.appColors.grey4),
                        textDirection: ui.TextDirection.ltr,
                      ),
                    ],
                  ),
                  assessmentDetailProvider.assessmentResponse!.data!
                              .instruction!.details!.displayScorecard !=
                          1
                      ? SizedBox()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Row(
                              children: [],
                            ),
                            Text(
                                '${tr('score')}: ${assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.score.toStringAsFixed(2)}'),
                          ],
                        ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}

class AssessmentAnswerTypes extends StatelessWidget {
  Color color;
  String text;

  AssessmentAnswerTypes(this.color, this.text, {super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: 10,
          width: 10,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25.0),
            color: color,
          ),
        ),
        const SizedBox(
          width: 5,
        ),
        Text(
          text,
          style: Styles.textRegular(
              size: 13,
              color: context.appColors.darkBlue.withValues(alpha: 0.7)),
        )
      ],
    );
  }
}
