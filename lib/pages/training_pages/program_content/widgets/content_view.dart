import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/common_web_view.dart';
import 'package:masterg/pages/training_pages/program_content/widgets/assigment_view.dart';
import 'package:masterg/pages/training_pages/program_content/widgets/intractive_view.dart';
import 'package:masterg/pages/training_pages/program_content/widgets/scorm_view.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

import 'assessment_view.dart';
import 'learning_shorts_view.dart';
import 'sessioin_view.dart';

class ContentPreview extends StatelessWidget {
  final dynamic selectedContent;
  const ContentPreview({super.key, this.selectedContent});

  @override
  Widget build(BuildContext context) {
    switch (selectedContent.runtimeType) {
      case (Sessions):
        return SessionView(
          sessions: selectedContent,
        );
      case (Assessments):
        return AssessmentView(assessment: selectedContent);
      case (Assignments):
        return AssigmentView(assigment: selectedContent);
      case (LearningShots):
        return LearningShortsView(
          learningShorts: selectedContent,
        );
      case (Scorm):
        return ScormView(
          scrom: selectedContent,
        );
      case (InteractiveContent):
        return IntractiveView(interactiveContent: selectedContent);
      default:
    }
    return Container(
      color: context.appColors.surface,
      height: MediaQuery.of(context).size.height * 0.24,
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/images/no_content_found.svg',
            color: context.appColors.grey2,
            height: height(context) * 0.07,
            fit: BoxFit.cover,
          ),
          SizedBox(
            height: 10,
          ),
          InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) {
                    return CommonWebView(
                        url:
                            'https://mecfuture.mec.edu.om/attemptH5pContentWebView/80?user_id=104990');
                  },
                ),
              );
            },
            child: Text(
              tr('no_content_found'),
              style: Styles.getRegularThemeStyle(context),
            ),
          ),
        ],
      ),
    );
  }
}
