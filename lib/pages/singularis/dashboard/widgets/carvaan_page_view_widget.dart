import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_content_resp.dart';
import 'package:masterg/data/models/response/home_response/gcarvaan_post_reponse.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/gcarvaan/comment/comment_view_page.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/carvaan_list_item.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/dots.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:masterg/utils/video_screen.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';

class CarvaanPageViewWidget extends StatefulWidget {
  final List<DashboardCarvanLimit>? carvaanList;
  final PageController pageController;
  final int selectedPage;
  final ValueChanged<int> onPageChanged;
  final MenuListProvider? menuProvider;
  final Function(int? like, dynamic contentId) updateLikeandViews;

  const CarvaanPageViewWidget({
    super.key,
    required this.carvaanList,
    required this.pageController,
    required this.selectedPage,
    required this.onPageChanged,
    required this.menuProvider,
    required this.updateLikeandViews,
  });

  @override
  State<CarvaanPageViewWidget> createState() => _CarvaanPageViewWidgetState();
}

class _CarvaanPageViewWidgetState extends State<CarvaanPageViewWidget> {
  @override
  Widget build(BuildContext context) {
    final carvaanList = widget.carvaanList;
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(color: context.appColors.surface),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                      padding: Utility().isRTL(context)
                          ? EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            )
                          : EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            ),
                      child: Text(
                        'Recent_community',
                        style: Styles.bold(
                            color: context.appColors.headingTitle, size: 14),
                      ).tr()),
                  Expanded(child: SizedBox()),
                  IconButton(
                      onPressed: () {
                        FirebaseAnalytics.instance.logEvent(
                            name: 'Recent_community_dashboard_view_list',
                            parameters: {
                              "latest_reels": 'view all',
                            });
                        widget.menuProvider?.updateCurrentIndex('/g-carvaan');
                      },
                      icon: Icon(
                        Icons.arrow_forward_ios,
                        color: context.appColors.headingTitle,
                        size: 20,
                      ))
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: SizedBox(
                  height: Utility().isRTL(context)
                      ? height(context) * 0.6
                      : height(context) * 0.55,
                  child: PageView.builder(
                    controller: widget.pageController,
                    itemCount: carvaanList?.length,
                    onPageChanged: widget.onPageChanged,
                    itemBuilder: (BuildContext context, int index) {
                      // final now = currentIndiaTime;

                      var millis =
                          int.parse(carvaanList![index].createdAt.toString());
                      DateTime date = DateTime.fromMillisecondsSinceEpoch(
                        millis * 1000,
                      );

                      return Container(
                        width: MediaQuery.of(context).size.width * 0.8,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border:
                                Border.all(color: context.appColors.divider)),
                        margin: EdgeInsets.all(8),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0,
                                    right: 8.0,
                                    top: 15.0,
                                    bottom: 8.0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Center(
                                      child: ClipOval(
                                          child: Image.network(
                                        '${carvaanList[index].profileImage}',
                                        height: 30,
                                        width: 30,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, url, error) {
                                          return SvgPicture.asset(
                                            'assets/images/default_user.svg',
                                            height: 30,
                                            width: 30,
                                            allowDrawingOutsideViewBox: true,
                                          );
                                        },
                                        loadingBuilder: (BuildContext context,
                                            Widget child,
                                            ImageChunkEvent? loadingProgress) {
                                          if (loadingProgress == null) {
                                            return child;
                                          }
                                          return Shimmer.fromColors(
                                            baseColor:
                                                context.appColors.shimmerBase,
                                            highlightColor: context
                                                .appColors.shimmerHighlight,
                                            child: Container(
                                                height: 50,
                                                margin:
                                                    EdgeInsets.only(left: 2),
                                                width: 50,
                                                decoration: BoxDecoration(
                                                  color:
                                                      context.appColors.surface,
                                                  shape: BoxShape.circle,
                                                )),
                                          );
                                        },
                                      )),
                                    ),
                                    Expanded(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: <Widget>[
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8.0,
                                                right: 8.0,
                                                top: 2.0),
                                            child: Text(
                                              Utility().decrypted128(
                                                  '${carvaanList[index].name}'),
                                              style: Styles.getTextRegularStyle(
                                                  context,
                                                  lineHeight: 1,
                                                  size: 14),
                                              maxLines: 1,
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8.0, right: 8.0),
                                            child: Text(
                                              Utility()
                                                  .calculateTimeDifferenceBetween(
                                                      DateTime.parse(date
                                                          .toString()
                                                          .substring(0, 19)),
                                                      currentIndiaTime!,
                                                      context),
                                              style:
                                                  Styles.getRegularThemeStyle(
                                                context,
                                                size: 12,
                                                lineHeight: 1.2,
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 8),
                                margin: EdgeInsets.only(bottom: 10),
                                width: MediaQuery.of(context).size.width * 0.8,
                                child: CarvaanListItem(
                                    description:
                                        carvaanList[index].description ?? ''),
                              ),
                              carvaanList[index]
                                              .resourcePath
                                              ?.contains('.mp4') ==
                                          true ||
                                      carvaanList[index]
                                              .resourcePath
                                              ?.contains('.mov') ==
                                          true
                                  ? SizedBox(
                                      height: 290,
                                      child: Center(
                                        child: VideoPlayerWidget(
                                          videoUrl:
                                              '${carvaanList[index].resourcePath}',
                                        ),
                                      ))
                                  : Image.network(
                                      '${carvaanList[index].resourcePath}',
                                      height: 290,
                                      width: double.infinity,
                                      fit: BoxFit.fitWidth),
                              Spacer(),
                              MultiProvider(
                                providers: [
                                  ChangeNotifierProvider<GCarvaanListModel>(
                                    create: (context) => GCarvaanListModel(
                                        carvaanList
                                            .map((e) => GCarvaanPostElement(
                                                id: e.id,
                                                commentCount:
                                                    e.commentCount ?? 0,
                                                likeCount: e.likeCount,
                                                userLiked: e.userLiked,
                                                programContentId:
                                                    e.programContentId))
                                            .toList()),
                                  ),
                                ],
                                child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 5.0,
                                      horizontal: 10.0,
                                    ),
                                    child: Consumer<GCarvaanListModel>(
                                      builder:
                                          (context, carvaanListModel, child) =>
                                              Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        mainAxisSize: MainAxisSize.max,
                                        children: <Widget>[
                                          InkWell(
                                            onTap: () {
                                              setState(() {
                                                if (carvaanListModel
                                                        .isLiked(index) ==
                                                    true) {
                                                  widget.updateLikeandViews(
                                                      0,
                                                      carvaanListModel
                                                          .list?[index].id);

                                                  carvaanListModel
                                                      .updateIsLiked(index, 0);

                                                  carvaanListModel
                                                      .decrementLike(index);
                                                } else {
                                                  widget.updateLikeandViews(
                                                      1,
                                                      carvaanListModel
                                                          .list?[index].id);

                                                  carvaanListModel
                                                      .updateIsLiked(index, 1);

                                                  carvaanListModel
                                                      .incrementLike(index);
                                                }
                                              });
                                            },
                                            child: Row(
                                              children: <Widget>[
                                                Padding(
                                                  padding:
                                                      Utility().isRTL(context)
                                                          ? EdgeInsets.only(
                                                              left: 4.0,
                                                            )
                                                          : EdgeInsets.only(
                                                              right: 4.0,
                                                            ),
                                                  child: SvgPicture.asset(
                                                    carvaanListModel.isLiked(
                                                                index) ==
                                                            false
                                                        ? 'assets/images/like_icon.svg'
                                                        : 'assets/images/liked_icon.svg',
                                                    height: 18.8,
                                                    width: 17.86,
                                                    color: carvaanListModel
                                                                .isLiked(
                                                                    index) ==
                                                            false
                                                        ? context.appColors
                                                            .headingTitle
                                                        : context
                                                            .appColors.primary,
                                                  ),
                                                ),
                                                Text(
                                                  () {
                                                    int likeCount =
                                                        (carvaanListModel
                                                                .list?[index]
                                                                .likeCount ??
                                                            0);

                                                    if (likeCount == 0) {
                                                      return tr('like');
                                                    }
                                                    if (likeCount == 1) {
                                                      return '$likeCount ${tr('like')}';
                                                    }
                                                    return '$likeCount ${tr('likes')}';
                                                  }(),
                                                  style: Styles.regular(
                                                      size: 12,
                                                      color: context.appColors
                                                          .headingTitle),
                                                ),
                                              ],
                                            ),
                                          ),
                                          InkWell(
                                            onTap: () {
                                              showModalBottomSheet(
                                                      context: context,
                                                      backgroundColor: context
                                                          .appColors
                                                          .appBarBackground,
                                                      isScrollControlled: true,
                                                      builder: (context) {
                                                        return FractionallySizedBox(
                                                          heightFactor: 0.7,
                                                          child:
                                                              CommentViewPage(
                                                            postId: carvaanList[
                                                                    index]
                                                                .id,
                                                            value:
                                                                carvaanListModel,
                                                          ),
                                                        );
                                                      })
                                                  .then((value) =>
                                                      setState(() {}));
                                            },
                                            child: Row(
                                              children: <Widget>[
                                                Padding(
                                                  padding:
                                                      Utility().isRTL(context)
                                                          ? EdgeInsets.only(
                                                              left: 4.0,
                                                            )
                                                          : EdgeInsets.only(
                                                              right: 4.0,
                                                            ),
                                                  child: SvgPicture.asset(
                                                    'assets/images/comment_icon.svg',
                                                    height: 18.8,
                                                    width: 17.86,
                                                    colorFilter:
                                                        ColorFilter.mode(
                                                            context.appColors
                                                                .headingTitle,
                                                            BlendMode.srcIn),
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  ),
                                                ),
                                                Text(
                                                  () {
                                                    int commentCount =
                                                        (carvaanListModel
                                                                .list?[index]
                                                                .commentCount ??
                                                            0);

                                                    if (commentCount == 0) {
                                                      return tr('comment');
                                                    }
                                                    if (commentCount == 1) {
                                                      return '$commentCount ${tr('comment')}';
                                                    }
                                                    return '$commentCount ${tr('comments')}';
                                                  }(),
                                                  style: Styles.regular(
                                                      size: 12,
                                                      color: context.appColors
                                                          .headingTitle),
                                                ),
                                              ],
                                            ),
                                          ),
                                          InkWell(
                                            onTap: () {
                                              carvaanList[index].description ??=
                                                  '';

                                              if (['mp4', 'mov'].contains(
                                                  '${carvaanList[index].resourcePath}'
                                                      .split('.')
                                                      .last)) {
                                                Utility.shortLink(
                                                        '${carvaanList[index].resourcePath}')
                                                    .then((value) {
                                                  String? s = carvaanList[index]
                                                              .description!
                                                              .length >
                                                          1000
                                                      ? carvaanList[index]
                                                          .description
                                                          ?.substring(0, 1000)
                                                      : '${carvaanList[index].description}...';
                                                  Share.share(
                                                      "$value ${s ?? ''}");
                                                });
                                              } else {
                                                if (Platform.isAndroid) {
                                                  try {
                                                    Utility().shareFile(context,
                                                        url:
                                                            '${carvaanList[index].resourcePath}',
                                                        text: carvaanList[index]
                                                                    .description!
                                                                    .length >
                                                                1000
                                                            ? carvaanList[index]
                                                                .description
                                                                ?.substring(
                                                                    0, 1000)
                                                            : '${carvaanList[index].description}...');
                                                  } catch (e, stacktrace) {
                                                    Log.v(
                                                        "stacktrace $e, $stacktrace");
                                                  }
                                                } else {
                                                  Utility.shortLink(
                                                          '${carvaanList[index].resourcePath}')
                                                      .then((value) {
                                                    String? msg = carvaanList[
                                                                    index]
                                                                .description!
                                                                .length >
                                                            1000
                                                        ? carvaanList[index]
                                                            .description
                                                            ?.substring(0, 1000)
                                                        : '${carvaanList[index].description}...';
                                                    Share.share(
                                                        '$value\n${msg ?? ''}]');
                                                  });
                                                }
                                              }
                                            },
                                            child: Row(
                                              children: <Widget>[
                                                Padding(
                                                  padding:
                                                      Utility().isRTL(context)
                                                          ? EdgeInsets.only(
                                                              left: 4.0,
                                                            )
                                                          : EdgeInsets.only(
                                                              right: 4.0,
                                                            ),
                                                  child: SvgPicture.asset(
                                                    'assets/images/share_icon.svg',
                                                    height: 18.8,
                                                    width: 17.86,
                                                    colorFilter:
                                                        ColorFilter.mode(
                                                            context.appColors
                                                                .headingTitle,
                                                            BlendMode.srcIn),
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  ),
                                                ),
                                                Text(
                                                  'share',
                                                  style: Styles.regular(
                                                      size: 12,
                                                      color: context.appColors
                                                          .headingTitle),
                                                ).tr()
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    )),
                              ),
                            ]),
                      );
                    },
                  ),
                ),
              ),
              SizedBox(
                height: 10.0,
              ),
              Dots(
                  index: widget.selectedPage,
                  postCount: carvaanList?.length as int),
              SizedBox(
                height: 20.0,
              )
            ],
          ),
        );
      },
    );
  }
}
