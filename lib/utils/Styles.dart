import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class Styles {
  // static const _NunitoBold = "Nunito_Bold";
  static const _nunitoLight = "Nunito_Light";
  static const _nunitoRegular = "Nunito_Regular";
  static const _nunitoExtraBold = "Nunito_ExtraBold";
  static const _nunitoSemiBold = "Nunito_SemiBold";

  static const _openSansBold = "OpenSansBold";
  static const _openSansSemiBold = "OpenSans_SemiBold";
  static const _openSansRegular = "OpenSans_Regular";

  static const _cairoBold = "CairoBold";
  static const _cairoSemiBold = "Cairo_SemiBold";
  static const _cairoRegular = "Cairo_Regular";

  static const _dMSansBold = "DMSans_Bold";
  static const _dMSansRegular = "DMSans_Regular";

  // Legacy static methods ( - use theme-aware versions instead)

  static TextStyle textBold(
      {double? size = 14,
      Color color = ColorConstants.darkBlue,
      double? lineHeight = 1.4}) {
    return TextStyle(
        fontSize: size,
        height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? lineHeight
            : lineHeight,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoBold
            : _cairoBold, //_NunitoBold
        color: color);
  }

  static TextStyle textExtraBold(
      {double? size = 14, Color color = ColorConstants.darkBlue}) {
    return TextStyle(
        fontSize: size,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoBold
            : _nunitoExtraBold,
        color: color);
  }

  static TextStyle textExtraBoldUnderline(
      {double size = 14, Color color = ColorConstants.darkBlue}) {
    return TextStyle(
        fontSize: size,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoBold
            : _openSansBold,
        color: color,
        decoration: TextDecoration.underline);
  }

  static TextStyle textRegular(
      {double? size = 14,
      Color color = ColorConstants.darkBlue,
      double? lineHeight = 1.4}) {
    return TextStyle(
        fontSize: size,
        height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? lineHeight
            : null,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoRegular
            : _nunitoRegular,
        color: color);
  }

  static TextStyle textItalic(
      {double size = 14, Color color = ColorConstants.darkBlue}) {
    return TextStyle(
        fontSize: size,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoRegular
            : _nunitoRegular,
        color: color,
        fontStyle: FontStyle.italic);
  }

  static TextStyle textSemiBold(
      {double? size = 14,
      Color color = ColorConstants.darkBlue,
      double? lineHeight = 1.4}) {
    return TextStyle(
      fontSize: size,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoSemiBold
          : _nunitoSemiBold,
      color: color,
      height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? lineHeight
          : null,
    );
  }

  static TextStyle textSemiBoldUndeline(
      {double size = 14, Color color = ColorConstants.darkBlue}) {
    return TextStyle(
        fontSize: size,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoBold
            : _openSansBold,
        color: color,
        decoration: TextDecoration.underline);
  }

  static TextStyle textLight(
      {double? size = 14, Color color = ColorConstants.darkBlue}) {
    return TextStyle(
      fontSize: size,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoRegular
          : _nunitoLight,
      color: color,
    );
  }

  // Theme-aware text style methods
  /// Get theme-aware text bold style
  static Widget textBoldTheme(BuildContext context,
      {double? size = 14,
      Color? color,
      double? lineHeight = 1.4,
      required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? lineHeight
                : lineHeight,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoBold
                : _cairoBold,
            color: color ?? context.headingTextColor,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware text extra bold style
  static Widget textExtraBoldTheme(BuildContext context,
      {double? size = 14, Color? color, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoBold
                : _nunitoExtraBold,
            color: color ?? context.headingTextColor,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware text extra bold underline style
  static Widget textExtraBoldUnderlineTheme(BuildContext context,
      {double size = 14, Color? color, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoBold
                : _openSansBold,
            color: color ?? context.headingTextColor,
            decoration: TextDecoration.underline,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware text regular style
  static Widget textRegularTheme(BuildContext context,
      {double? size = 14,
      Color? color,
      double? lineHeight = 1.4,
      required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? lineHeight
                : null,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoRegular
                : _nunitoRegular,
            color: color ?? context.bodyTextColor,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware text italic style
  static Widget textItalicTheme(BuildContext context,
      {double size = 14, Color? color, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoRegular
                : _nunitoRegular,
            color: color ?? context.bodyTextColor,
            fontStyle: FontStyle.italic,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware text semi bold style
  static Widget textSemiBoldTheme(BuildContext context,
      {double? size = 14,
      Color? color,
      double? lineHeight = 1.4,
      required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoSemiBold
                : _nunitoSemiBold,
            color: color ?? context.headingTextColor,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? lineHeight
                : null,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware text semi bold underline style
  static Widget textSemiBoldUnderlineTheme(BuildContext context,
      {double size = 14, Color? color, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoBold
                : _openSansBold,
            color: color ?? context.headingTextColor,
            decoration: TextDecoration.underline,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware text light style
  static Widget textLightTheme(BuildContext context,
      {double? size = 14, Color? color, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoRegular
                : _nunitoLight,
            color: color ?? context.bodyTextColor,
          ),
          child: child,
        );
      },
    );
  }

  // Legacy white text methods ( - use theme-aware versions instead)

  static dynamic boldWhite({double? size}) {
    return textBold(size: size, color: ColorConstants.white);
  }

  static dynamic regularWhite({double? size, double? lineHeight = 1.4}) {
    return textRegular(
        size: size, color: ColorConstants.white, lineHeight: lineHeight);
  }

  static dynamic semiBoldWhite({double? size}) {
    return textSemiBold(size: size, color: ColorConstants.white);
  }

  static dynamic extraBoldWhite({double? size}) {
    return textExtraBold(size: size, color: ColorConstants.white);
  }

  static dynamic lightWhite({double? size}) {
    return textLight(size: size, color: ColorConstants.white);
  }

  // Legacy style methods ( - use theme-aware versions instead)

  static TextStyle bold(
      {double size = 16,
      Color color = ColorConstants.black,
      double? lineHeight = 1.4}) {
    return TextStyle(
        fontSize: size * 1.0,
        height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? lineHeight
            : null,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoBold
            : _openSansBold,
        color: color,
        fontWeight: FontWeight.w700);
  }

  static TextStyle semibold(
      {double size = 18,
      Color color = ColorConstants.black,
      double? lineHeight = 1.4}) {
    return TextStyle(
        fontSize: size * 1.0,
        height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? lineHeight
            : null,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoSemiBold
            : _openSansSemiBold,
        color: color,
        fontWeight: FontWeight.w600);
  }

  static TextStyle lineThrough(
      {double size = 18, Color color = ColorConstants.black}) {
    return TextStyle(
      fontSize: size * 1.0,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoSemiBold
          : _openSansSemiBold,
      decoration: TextDecoration.lineThrough,
    );
  }

  static TextStyle regular(
      {double size = 16,
      Color color = ColorConstants.grey2,
      double? lineHeight = 1.4}) {
    return TextStyle(
        fontSize: size * 1.0,
        height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? lineHeight
            : null,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoRegular
            : _openSansRegular,
        color: color,
        fontWeight: FontWeight.w400);
  }

  static TextStyle otp(
      {double size = 16,
      Color color = ColorConstants.grey2,
      double? letterSpacing}) {
    return TextStyle(
        fontSize: size * 1.0,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoRegular
            : _openSansRegular,
        color: color,
        letterSpacing: letterSpacing,
        fontWeight: FontWeight.w400);
  }

  static TextStyle dMSansbold(
      {double size = 18, Color color = ColorConstants.black}) {
    return TextStyle(
        fontSize: size,
        fontFamily: _dMSansBold,
        color: color,
        fontWeight: FontWeight.w700);
  }

  static TextStyle dMSansregular(
      {double size = 16, Color color = ColorConstants.grey2}) {
    return TextStyle(
        fontSize: size,
        fontFamily: _dMSansRegular,
        color: color,
        fontWeight: FontWeight.w400);
  }

  static TextStyle regularUnderline(
      {double size = 16,
      Color color = ColorConstants.grey2,
      double? lineHeight = 1.4}) {
    return TextStyle(
        fontSize: size * 1.0,
        height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? lineHeight
            : null,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoRegular
            : _openSansRegular,
        color: color,
        fontWeight: FontWeight.w400,
        decoration: TextDecoration.underline);
  }

  // Theme-aware white text methods
  /// Get theme-aware bold white text style (for dark backgrounds)
  static Widget boldWhiteTheme(BuildContext context,
      {double? size, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? 1.4
                : 1.4,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoBold
                : _cairoBold,
            color: context
                .appColors.textWhite, // Always white for dark backgrounds
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware regular white text style (for dark backgrounds)
  static Widget regularWhiteTheme(BuildContext context,
      {double? size, double? lineHeight = 1.4, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? lineHeight
                : null,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoRegular
                : _nunitoRegular,
            color: context
                .appColors.textWhite, // Always white for dark backgrounds
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware semi bold white text style (for dark backgrounds)
  static Widget semiBoldWhiteTheme(BuildContext context,
      {double? size, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoSemiBold
                : _nunitoSemiBold,
            color: context
                .appColors.textWhite, // Always white for dark backgrounds
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? 1.4
                : null,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware extra bold white text style (for dark backgrounds)
  static Widget extraBoldWhiteTheme(BuildContext context,
      {double? size, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoBold
                : _nunitoExtraBold,
            color: context
                .appColors.textWhite, // Always white for dark backgrounds
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware light white text style (for dark backgrounds)
  static Widget lightWhiteTheme(BuildContext context,
      {double? size, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoRegular
                : _nunitoLight,
            color: context
                .appColors.textWhite, // Always white for dark backgrounds
          ),
          child: child,
        );
      },
    );
  }

  // Theme-aware general style methods
  /// Get theme-aware bold text style
  static Widget boldTheme(BuildContext context,
      {double size = 16, double? lineHeight = 1.4, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size * 1.0,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? lineHeight
                : null,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoBold
                : _openSansBold,
            color: context.headingTextColor,
            fontWeight: FontWeight.w700,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware semibold text style
  static Widget semiboldTheme(BuildContext context,
      {double size = 18, double? lineHeight = 1.4, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size * 1.0,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? lineHeight
                : null,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoSemiBold
                : _openSansSemiBold,
            color: context.headingTextColor,
            fontWeight: FontWeight.w600,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware regular text style
  static Widget regularTheme(BuildContext context,
      {double size = 16, double? lineHeight = 1.4, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size * 1.0,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? lineHeight
                : null,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoRegular
                : _openSansRegular,
            color: context.bodyTextColor,
            fontWeight: FontWeight.w400,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware line through text style
  static Widget lineThroughTheme(BuildContext context,
      {double size = 18, Color? color, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size * 1.0,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoSemiBold
                : _openSansSemiBold,
            color: color ?? context.bodyTextColor,
            decoration: TextDecoration.lineThrough,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware OTP text style
  static Widget otpTheme(BuildContext context,
      {double size = 16,
      Color? color,
      double? letterSpacing,
      required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size * 1.0,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoRegular
                : _openSansRegular,
            color: color ?? context.otpTextColor,
            letterSpacing: letterSpacing,
            fontWeight: FontWeight.w400,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware DMSans bold text style
  static Widget dmSansBoldTheme(BuildContext context,
      {double size = 18, Color? color, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: _dMSansBold,
            color: color ?? context.headingTextColor,
            fontWeight: FontWeight.w700,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware DMSans regular text style
  static Widget dmSansRegularTheme(BuildContext context,
      {double size = 16, Color? color, required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size,
            fontFamily: _dMSansRegular,
            color: color ?? context.bodyTextColor,
            fontWeight: FontWeight.w400,
          ),
          child: child,
        );
      },
    );
  }

  /// Get theme-aware regular underline text style
  static Widget regularUnderlineTheme(BuildContext context,
      {double size = 16,
      Color? color,
      double? lineHeight = 1.4,
      required Widget child}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return DefaultTextStyle(
          style: TextStyle(
            fontSize: size * 1.0,
            height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? lineHeight
                : null,
            fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
                ? _cairoRegular
                : _openSansRegular,
            color: color ?? context.bodyTextColor,
            fontWeight: FontWeight.w400,
            decoration: TextDecoration.underline,
          ),
          child: child,
        );
      },
    );
  }

  // Utility methods to get TextStyle directly (for backward compatibility)
  /// Get theme-aware TextStyle for bold text
  static TextStyle getBoldThemeStyle(BuildContext context,
      {double size = 16, double? lineHeight = 1.4}) {
    return TextStyle(
      fontSize: size * 1.0,
      height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? lineHeight
          : null,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoBold
          : _openSansBold,
      color: context.headingTextColor,
      fontWeight: FontWeight.w700,
    );
  }

  /// Get theme-aware TextStyle for semibold text
  static TextStyle getSemiboldThemeStyle(BuildContext context,
      {double size = 18, double? lineHeight = 1.4}) {
    return TextStyle(
      fontSize: size * 1.0,
      height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? lineHeight
          : null,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoSemiBold
          : _openSansSemiBold,
      color: context.headingTextColor,
      fontWeight: FontWeight.w600,
    );
  }

  /// Get theme-aware TextStyle for regular text
  static TextStyle getRegularThemeStyle(BuildContext context,
      {double size = 16, double? lineHeight = 1.4}) {
    return TextStyle(
      fontSize: size * 1.0,
      height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? lineHeight
          : null,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoRegular
          : _openSansRegular,
      color: context.bodyTextColor,
      fontWeight: FontWeight.w400,
    );
  }

  static TextStyle getTextRegularStyle(BuildContext context,
      {double? size = 14, Color? color, double? lineHeight = 1.4}) {
    return TextStyle(
        fontSize: size,
        height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? lineHeight
            : null,
        fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
            ? _cairoRegular
            : _nunitoRegular,
        color: color ??
            (context.isDarkMode
                ? ColorConstants.white
                : ColorConstants.darkBlue));
  }

  /// Get theme-aware TextStyle for text bold
  static TextStyle getTextBoldThemeStyle(BuildContext context,
      {double? size = 14, double? lineHeight = 1.4}) {
    return TextStyle(
      fontSize: size,
      height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? lineHeight
          : lineHeight,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoBold
          : _cairoBold,
      color: context.headingTextColor,
    );
  }

  /// Get theme-aware TextStyle for text regular
  static TextStyle getTextRegularThemeStyle(BuildContext context,
      {double? size = 14, double? lineHeight = 1.4}) {
    return TextStyle(
      fontSize: size,
      height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? lineHeight
          : null,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoRegular
          : _nunitoRegular,
      color: context.bodyTextColor,
    );
  }

  /// Get theme-aware TextStyle for text italic
  static TextStyle getTextItalicThemeStyle(BuildContext context,
      {double size = 14, double? lineHeight = 1.4}) {
    return TextStyle(
      fontSize: size,
      height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? lineHeight
          : null,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoRegular
          : _nunitoRegular,
      color: context.bodyTextColor,
      fontStyle: FontStyle.italic,
    );
  }

  /// Get theme-aware TextStyle for text semibold
  static TextStyle getTextSemiBoldThemeStyle(BuildContext context,
      {double? size = 14, double? lineHeight = 1.4}) {
    return TextStyle(
      fontSize: size,
      fontFamily: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? _cairoSemiBold
          : _nunitoSemiBold,
      color: context.headingTextColor,
      height: APK_DETAILS['package_name'] == 'com.singulariswow.mec'
          ? lineHeight
          : null,
    );
  }
}
