import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/response/home_response/my_assessment_response.dart';
import '../../../data/models/response/home_response/test_review_response.dart';
import '../../../utils/styles.dart';
import '../../../utils/widget_size.dart';
import '../../../utils/theme/theme_extensions.dart';

class AssessmentYourReportPage extends StatefulWidget {
  final int? contentId;
  final int? programId;
  final int? displayScorecard;
  const AssessmentYourReportPage(
      {super.key, this.programId, this.contentId, this.displayScorecard});

  @override
  State<AssessmentYourReportPage> createState() =>
      _AssessmentYourReportPageState();
}

class _AssessmentYourReportPageState extends State<AssessmentYourReportPage> {
  List<AssessmentList>? assessmentList = [];
  var isLoading = true;
  late HomeBloc _authBloc;
  final List<TestReviewBean> _list = [];
  int? currentQuestionId;
  int skippedCount = 0;
  int correctCount = 0;
  int questionCount = 0;
  dynamic scoreCount = 0.0;
  int? rank = 0;
  int program = 0;
  String selectedOption = '';
  List selectedOptionList = [];

  @override
  void initState() {
    super.initState();
  }

  void _handleAttemptTestResponse(ReviewTestState state) {
    try {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          setState(() {
            isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          if (state.response!.data != null) {
            _list.clear();
            selectedOptionList.clear();
            for (int i = 0;
                i < state.response!.data!.assessmentReview!.questions!.length;
                i++) {
              _list.add(
                TestReviewBean(
                    question:
                        state.response!.data!.assessmentReview!.questions![i],
                    id: state.response!.data!.assessmentReview!.questions![i]
                        .questionId,
                    title: state.response!.data!.assessmentReview!.questions![i]
                        .question),
              );

              questionCount =
                  state.response!.data!.assessmentReview!.questionCount!;

              if (_list[i].question!.isCorrect == 0) {
                skippedCount++;
              }
              if (_list[i].question!.isCorrect == 1) {
                correctCount++;
              }
              scoreCount = state.response!.data!.assessmentReview!.score!;
              rank = state.response?.data?.assessmentReview?.leaderboard
                      ?.studentRank ??
                  0;

              int count = state.response!.data!.assessmentReview!.questions![i]
                  .questionOptions!.length;
              if (count == 1) {
                selectedOption = 'a';
              } else if (count == 2) {
                selectedOption = 'b';
              } else if (count == 3) {
                selectedOption = 'c';
              } else if (count == 4) {
                selectedOption = 'd';
              } else {
                selectedOption = '';
              }
              selectedOptionList.add(selectedOption);
            }

            if (_list.isNotEmpty) {
              currentQuestionId = _list.first.question!.questionId;
            }
          }
          setState(() {
            isLoading = false;
          });
          break;
        case ApiStatus.ERROR:
          setState(() {
            isLoading = false;
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.background,
      appBar: AppBar(
        title: Text('your_report',
                style: Styles.getBoldThemeStyle(context, size: 18))
            .tr(),
        centerTitle: true,
        backgroundColor: context.appColors.surface,
        elevation: 0.0,
      ),
      body: _mainBody(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            InkWell(
              onTap: () => Navigator.pop(context),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.9,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  gradient: LinearGradient(colors: [
                    context.appColors.gradientLeft,
                    context.appColors.gradientRight,
                  ]),
                ),
                child: Center(
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: Text(
                      'back',
                      style: Styles.bold(color: context.appColors.textWhite),
                    ).tr(),
                  ),
                ),
              ),
            ),
          ]),
    );
  }

  BlocManager _mainBody() {
    _authBloc = BlocProvider.of<HomeBloc>(context);
    return BlocManager(
        initState: (context) {
          if (_list.isEmpty) {
            _authBloc.add(
              ReviewTestEvent(
                  request:
                      '${widget.contentId}?program_id=${widget.programId}'),
            );
          }
        },
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is ReviewTestState) _handleAttemptTestResponse(state);
          },
          child: _list.isNotEmpty
              ? SizedBox(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 150,
                        child: Row(
                          children: <Widget>[
                            widget.displayScorecard == 1
                                ? Expanded(
                                    flex: 1,
                                    child: Container(
                                      width: MediaQuery.of(context).size.width *
                                          0.25,
                                      decoration: BoxDecoration(),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'score',
                                            style: Styles.textRegular(
                                                size: 16,
                                                color: context
                                                    .appColors.textBlack),
                                          ).tr(),
                                          Padding(
                                            padding:
                                                const EdgeInsets.only(top: 6.0),
                                            child: Text(
                                                '${scoreCount.toStringAsFixed(2)}',
                                                style: Styles.dMSansbold(
                                                    size: 20,
                                                    color: context
                                                        .appColors.textBlack)),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                : SizedBox(),
                            Container(
                              height: WidgetSize.REPORT_LINE_HEIGHT,
                              width: 1,
                              color: context.appColors.divider,
                            ),
                            Expanded(
                              flex: 1,
                              child: Container(
                                width: MediaQuery.of(context).size.width * 0.25,
                                decoration: BoxDecoration(),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'rank',
                                      style: Styles.textRegular(
                                          size: 16,
                                          color: context.appColors.textBlack),
                                    ).tr(),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 6.0),
                                      child: Text('$rank',
                                          style: Styles.dMSansbold(
                                              size: 20,
                                              color:
                                                  context.appColors.textBlack)),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              height: WidgetSize.REPORT_LINE_HEIGHT,
                              width: 1,
                              color: Colors.grey[200],
                            ),
                            Expanded(
                              flex: 1,
                              child: Container(
                                width: MediaQuery.of(context).size.width * 0.25,
                                decoration: BoxDecoration(),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'correct',
                                      style: Styles.textRegular(
                                          size: 16,
                                          color: context.appColors.textBlack),
                                    ).tr(),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 6.0),
                                      child: Text(
                                          '${correctCount.toString()}/${questionCount.toString()}',
                                          style: Styles.dMSansbold(
                                              size: 20,
                                              color:
                                                  context.appColors.textBlack)),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              height: WidgetSize.REPORT_LINE_HEIGHT,
                              width: 1,
                              color: Colors.grey[200],
                            ),
                            Expanded(
                              flex: 1,
                              child: Container(
                                width: MediaQuery.of(context).size.width * 0.25,
                                decoration: BoxDecoration(),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'skipped',
                                      style: Styles.textRegular(
                                          size: 16,
                                          color: context.appColors.textBlack),
                                    ).tr(),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 6.0),
                                      child: Text(skippedCount.toString(),
                                          style: Styles.dMSansbold(
                                              size: 20,
                                              color:
                                                  context.appColors.textBlack)),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 30.0, bottom: 20.0),
                        child: Text('your_answer',
                                style: Styles.textRegular(
                                    size: 16,
                                    color: context.appColors.textBlack))
                            .tr(),
                      ),
                      SizedBox(
                        height: MediaQuery.of(context).size.height - 350,
                        child: GridView.builder(
                          padding: EdgeInsets.only(left: 10.0, right: 10.0),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 5),
                          itemCount:
                              min(_list.length, selectedOptionList.length),
                          itemBuilder: (BuildContext context, int index) {
                            return _list[index].question!.isCorrect != 0
                                ? Card(
                                    color: _list[index].question!.isCorrect == 1
                                        ? context.appColors.green
                                        : context.appColors.error,
                                    child: Center(
                                      child: Text(
                                        '${index + 1}: ${selectedOptionList[index]}',
                                        style: Styles.textRegular(
                                            size: 16,
                                            color: context.appColors.textWhite),
                                      ),
                                    ),
                                  )
                                : Card(
                                    color: context.appColors.grey4,
                                    child: Center(
                                      child: Text(
                                        '${index + 1}',
                                        style: Styles.textRegular(
                                            size: 16,
                                            color: context.appColors.textWhite),
                                      ),
                                    ),
                                  );
                          },
                        ),
                      ),
                    ],
                  ),
                )
              : _emptyBody(),
        ));
  }

  Widget _emptyBody() {
    return GridView.builder(
      padding: EdgeInsets.only(left: 10.0, right: 10.0, top: 30.0),
      gridDelegate:
          const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 5),
      itemCount: 10,
      itemBuilder: (BuildContext context, int index) {
        return Shimmer.fromColors(
          baseColor: context.appColors.shimmerBase,
          highlightColor: context.appColors.shimmerHighlight,
          child: Card(
            color: context.appColors.grey3,
          ),
        );
      },
    );
  }
}
