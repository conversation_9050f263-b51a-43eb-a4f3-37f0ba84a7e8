import 'dart:convert';
import 'dart:math' as math;
import 'dart:developer';
import 'dart:io';
import 'dart:async';
// import 'package:aws_s3_upload/aws_s3_upload.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:devicelocale/devicelocale.dart';
import 'package:dio/dio.dart' as dio_pack;
import 'package:easy_localization/easy_localization.dart';
import 'package:encrypt/encrypt.dart' as enc;
import 'package:encrypt/encrypt.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:get/get.dart';
import 'package:masterg/data/api/api_constants.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/models/response/home_response/category_response.dart';
import 'package:masterg/data/models/response/home_response/timestamp_resp.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';

import 'package:masterg/pages/preboarding_pages/full_screen_preboarding.dart';
import 'package:masterg/pages/preboarding_pages/preboarding_page.dart';
import 'package:masterg/utils/log.dart';
import 'package:masterg/utils/config.dart';
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart' as vt;
import '../pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'styles.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'dart:ui' as ui;

class Utility {
  bool isConnected = true;

  static Future<bool> checkNetwork() async {
    if (kIsWeb) return true;
    bool isConnected = false;
    try {
      final result = await InternetAddress.lookup('google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        isConnected = true;
      }
    } on SocketException catch (_) {
      isConnected = false;
    }
    if (!isConnected && !Get.isSnackbarOpen) {
      Get.rawSnackbar(
          messageText: Text('please_connect_internet',
                  style:
                      TextStyle(color: const Color(0xFFFFFFFF), fontSize: 14))
              .tr(),
          isDismissible: true,
          duration: const Duration(seconds: 4),
          backgroundColor: const Color(0xFF000000),
          icon: Icon(
            Icons.wifi_off,
            color: const Color(0xFFFFFFFF),
            size: 35,
          ),
          margin: EdgeInsets.zero,
          snackStyle: SnackStyle.GROUNDED);
    }
    // else if (isConnected && Get.isSnackbarOpen) Get.closeCurrentSnackbar();
    return isConnected;
  }

  static void hideKeyboard() {
    SystemChannels.textInput.invokeMethod('TextInput.hide');
  }

  bool isRTL(BuildContext context) {
    return '${Directionality.of(context)}' == 'TextDirection.rtl';
  }

  static ui.TextDirection setDirection(isRTL) {
    return isRTL ? ui.TextDirection.rtl : ui.TextDirection.ltr;
  }

  static String fileExtension({required String filePath}) {
    return filePath.split('.').last;
  }

  String calculateTimeDifferenceBetween(
      DateTime startDate, DateTime endDate, context) {
    int seconds = endDate.difference(startDate).inSeconds.abs();
    //log("curren titme i ${seconds} and ${currentIndiaTime}");
    if (seconds < 60) {
      if (seconds.abs() < 4) return tr('now');
      return '${seconds.abs()} ${tr('second')}';
    } else if (seconds >= 60 && seconds < 3600)
      return '${startDate.difference(endDate).inMinutes.abs()} ${tr('mins')}';
    else if (seconds >= 3600 && seconds < 86400)
      return '${startDate.difference(endDate).inHours.abs()} ${tr('hour')}';
    else {
      int days = startDate.difference(endDate).inDays.abs();
      if (days < 30 && days > 7) {
        return '${(startDate.difference(endDate).inDays ~/ 7).abs()} ${tr('week')}';
      }
      if (days > 30) {
        int month = (startDate.difference(endDate).inDays ~/ 30).abs();
        return '$month ${tr('months')}';
      } else {
        return '${startDate.difference(endDate).inDays.abs()} ${tr('day')}';
      }
    }
  }

  static String convertSeconds(int seconds) {
    log("total secons are $seconds");
    const int secondsPerMinute = 60;
    const int secondsPerHour = 3600;
    const int secondsPerDay = 86400;
    const int secondsPerMonth = 2629746; // Average month length
    const int secondsPerYear = 31556952; // Average year length

    int remainingSeconds = seconds;

    int years = remainingSeconds ~/ secondsPerYear;
    remainingSeconds %= secondsPerYear;

    int months = remainingSeconds ~/ secondsPerMonth;
    remainingSeconds %= secondsPerMonth;

    int days = remainingSeconds ~/ secondsPerDay;
    remainingSeconds %= secondsPerDay;

    int hours = remainingSeconds ~/ secondsPerHour;
    remainingSeconds %= secondsPerHour;

    int minutes = remainingSeconds ~/ secondsPerMinute;
    remainingSeconds %= secondsPerMinute;

    int secs = remainingSeconds;

    if (kDebugMode) {
      log('Years: $years');
      log('Months: $months');
      log('Days: $days');
      log('Hours: $hours');
      log('Minutes: $minutes');
      log('Seconds: $secs');
    }

    if (years != 0) return '$years years';
    if (months != 0) return '$months months';
    if (days != 0) return '$days days';
    if (hours != 0) return '$hours hours';
    if (minutes != 0) return '$minutes minutes';
    if (secs != 0) return '$secs seconds';
    return '';
  }

  Future<bool> checkConnection() async {
    try {
      final result = await InternetAddress.lookup('example.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      }
    } on SocketException catch (_) {
      return false;
    }
    return false;
  }

  static Future<String?> getCurrentLocale() async {
    String? currentLocale0;
    try {
      final currentLocale = await Devicelocale.currentLocale;
      currentLocale0 = currentLocale;
      return currentLocale0;
    } on PlatformException {
      Log.v("Error obtaining current locale");
    }

    return '';
  }

  static Future<String> saveTemporarily(File file) async {
    final appDir = await getTemporaryDirectory();
    final tempPath = appDir.path;
    final fileName = file.path.split('/').last;
    final tempFile = await file.copy('$tempPath/$fileName');

    log('Image saved temporarily at: ${tempFile.path}');
    return tempFile.path;
  }

  static Future<void> removeAllFilesFromCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      List<FileSystemEntity> files = cacheDir.listSync();

      for (FileSystemEntity file in files) {
        if (file is File) {
          // Check if the entity is a file before attempting to delete
          await file.delete();
        }
      }

      log('All files removed from the cache.');
    } catch (e) {
      log('Error removing files from the cache: $e');
    }
  }

  ///Take seconds and input and returns duration in 02:30 format (mm:ss)
  static String showDuration(int sec) {
    int hours = sec ~/ 3600;
    int minutes = (sec % 3600) ~/ 60;
    int seconds = sec % 60;

    String h = '$hours'.padLeft(2, '0');
    String m = '$minutes'.padLeft(2, '0');
    String s = '$seconds'.padLeft(2, '0');

    if (hours > 0) {
      return '$h:$m:$s';
    } else {
      return '$m:$s';
    }
  }

  bool isHtml(String input) {
    // A simple regex to check if the string contains HTML tags
    final RegExp htmlTagRegExp = RegExp(r"<[^>]+>", caseSensitive: false);
    return htmlTagRegExp.hasMatch(input);
  }

  bool isMathEquation(String htmlContent) {
    // RegEx to find LaTeX equations in img src
    final RegExp mathPattern = RegExp(
      r'src="https:\/\/latex\.codecogs\.com\/svg\.image\?.*?"',
      caseSensitive: false,
    );

    // Check if the pattern is found
    return mathPattern.hasMatch(htmlContent);
  }

  static Future<String?> getThumnail(String url) async {
    // final directory = await getExternalStorageDirectory();
    log("get thumanil of file $url", name: "your_filename.dart");

    String? localThumbnail = Preference.getString(url);
    if (localThumbnail != null && localThumbnail != '') return localThumbnail;

    int index = url.lastIndexOf('/');
    String fileName =
        '${url.split('/').last.replaceAll(' ', '_').split('.').first}.jpg';
    String thumbnailPath = url.replaceRange(index, null, '/$fileName');
    log("get thumanil of $thumbnailPath", name: "your_filename.dart");

    try {
      final fileName = await vt.VideoThumbnail.thumbnailFile(
        video: url,
        thumbnailPath: thumbnailPath.contains('http') ? null : thumbnailPath,
        imageFormat: vt.ImageFormat.JPEG,
        maxHeight: 120,
        quality: 75,
      );
      Preference.setString(url, '$fileName');
      return fileName;
    } catch (e) {
      log("thumnail url is e $e");
    }
    return null;
  }

  var currentLocation;

  static void showSnackBar(
      {String? message,
      required BuildContext? scaffoldContext,
      int miliSec = 1500}) {
    if (scaffoldContext != null) {
      ScaffoldMessenger.of(scaffoldContext).showSnackBar(SnackBar(
          duration: Duration(milliseconds: miliSec),
          content: Text(
            message ?? "",
            style: Styles.textRegular(
                color: scaffoldContext.appColors.primaryForeground),
          ),
          backgroundColor: scaffoldContext.appColors.pureBlack));
    }
  }

  static String convertDateFromMillis(int timeInMillis, String newFormat,
      {bool isUTC = false}) {
    return DateFormat(newFormat).format(DateTime.fromMillisecondsSinceEpoch(
      timeInMillis * 1000,
      isUtc: isUTC,
    ));
  }

  static void tryCatch({required Function fn}) {
    try {
      fn();
    } catch (e, stacktrace) {
      log("Expcetion error: $e", name: 'exception error log');
      debugPrint(
        "Expcetion stacktrace: $stacktrace",
      );
    } finally {
      // ignore: control_flow_in_finally
      return;
    }
  }

  static bool isExpired(int? timeInMillis, DateTime? now) {
    return now!.millisecondsSinceEpoch ~/ 1000 > timeInMillis!;
  }

  static bool isBetween(int startTime, int endTime, DateTime now) {
    return now.millisecondsSinceEpoch ~/ 1000 > startTime &&
        now.millisecondsSinceEpoch ~/ 1000 < endTime;
  }

  static int classStatus(
      int startTime, int endTime, DateTime currentIndiaTime) {
    int currentTime = currentIndiaTime.millisecondsSinceEpoch ~/ 1000;

    DateTime utcStartDateTime =
        DateTime.fromMillisecondsSinceEpoch(startTime * 1000, isUtc: true);
    DateTime utcEndDateTime =
        DateTime.fromMillisecondsSinceEpoch(endTime * 1000, isUtc: true);
    DateTime utccurrentTime =
        DateTime.fromMillisecondsSinceEpoch(currentTime * 1000, isUtc: true);

    // Convert to IST (UTC+5:30)
    DateTime istStartDateTime =
        utcStartDateTime.add(Duration(hours: 5, minutes: 30));
    DateTime istEndDateTime =
        utcEndDateTime.add(Duration(hours: 5, minutes: 30));
    DateTime istDateTimeCCCCC =
        utccurrentTime.add(Duration(hours: 5, minutes: 30));

    int currentStartTime = istStartDateTime.millisecondsSinceEpoch ~/ 1000;
    int currentEndTime = istEndDateTime.millisecondsSinceEpoch ~/ 1000;
    int currentTimeCCCC = istDateTimeCCCCC.millisecondsSinceEpoch ~/ 1000;

    //if (currentTime > startTime && currentTime < endTime)
    if (currentTimeCCCC > currentStartTime &&
        currentTimeCCCC < currentEndTime) {
      return 0;
      //else if (currentTime < startTime)
    } else if (currentTimeCCCC < currentStartTime) {
      return 1;
    } else {
      return 2;
    }
  }

  // static int classStatusAsync(int startTime, int endTime, int? millisecondsSinceEpoch) {
  //   // if(millisecondsSinceEpoch == null) return 2;
  //  log('hello india : ${getIndianDateNow()}');
  //   double currentTime = getIndianDateNow().millisecondsSinceEpoch / 1000;
  //   if (currentTime > startTime && currentTime < endTime)
  //     return 0;
  //   else if (currentTime < startTime)
  //     return 1;
  //   else
  //     return 2;
  // }

  static String convertCourseTime(int? timeInMillis, String newFormat,
      {bool isUTC = false}) {
    return DateFormat(newFormat).format(DateTime.fromMillisecondsSinceEpoch(
        timeInMillis! * 1000,
        isUtc: isUTC));
  }

  static String convertGMTTime(int? timeInMillis) {
    // Convert to UTC
    DateTime utcDate =
        DateTime.fromMillisecondsSinceEpoch(timeInMillis! * 1000, isUtc: true);
    // Format as 11:30:00 AM GMT
    String formatted = DateFormat('hh:mm:ss a').format(utcDate);
    return formatted;
  }

  static Future<bool> _requestPermissions() async {
    var permission = await Permission.storage.request().isGranted;
    return permission;
  }

  static Future<void> _startDownload(
      String savePath, resourcePath, context) async {
    Map<String, dynamic> result = {
      'isSuccess': false,
      'filePath': null,
      'error': null,
    };
    final dio_pack.Dio dio = dio_pack.Dio();

    try {
      final response = await dio.download(resourcePath!, savePath,
          onReceiveProgress: (r, t) {
        _onReceiveProgress(r, t, context);
      });
      result['isSuccess'] = response.statusCode == 200;
      result['filePath'] = savePath;
      if (response.statusCode == 200) {
        await OpenFilex.open(savePath);
      }
      Log.v(savePath);
    } catch (ex) {
      Log.v(ex);
      result['error'] = ex.toString();
    }
  }

  static void _onReceiveProgress(int received, int total, context) {
    if (received == total) {
      Utility.showSnackBar(
          scaffoldContext: context, message: tr('download_started'));
    }
    if (total != -1) {}
  }

  static String pascalCase(String input) {
    List<String> words = input.split(' ');

    List<String> capitalizedWords = words.map((word) {
      if (word.isNotEmpty) {
        return word[0].toUpperCase() + word.substring(1);
      } else {
        return word;
      }
    }).toList();

    return capitalizedWords.join(' ');
  }

  static Future<void> downloadFile(resourcePath, BuildContext context) async {
    try {
      var dir = await getApplicationDocumentsDirectory();

      final isPermissionStatusGranted = await _requestPermissions();
      DeviceInfoPlugin plugin = DeviceInfoPlugin();

      late AndroidDeviceInfo android;
      try {
        android = await plugin.androidInfo;
      } catch (e) {
        Log.v("exception file download $e");
      }
      if (Platform.isIOS ||
          isPermissionStatusGranted ||
          android.version.sdkInt >= 33) {
        String ext = resourcePath!.split(".").last;
        final savePath = path.join(
            dir.path, "${resourcePath?.split("/").last.split(".").first}.$ext");
        if (await File(savePath).exists()) {
          await OpenFilex.open(savePath);
        } else {
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('download_started'));
          await _startDownload(savePath, resourcePath, context);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        log('Exception is $e');
      }
    }
  }

  static String getDiffInMin(int start, int end) {
    var diff = end - start;
    DateTime time = DateTime.fromMillisecondsSinceEpoch(diff * 1000).toUtc();
    //convert time to minutes
    int minutes = time.minute;
    return minutes.toString();
  }

  static String convertDateFormat(DateTime date,
      {String format = 'MM/dd/yyyy'}) {
    String formatted = '';
    var formatter = DateFormat(format);
    formatted = formatter.format(date);
    return formatted;
  }

  static String getDateFromDate(String data) {
    var outputDate = '';
    if (data.isNotEmpty) {
      var inputFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
      var inputDate = inputFormat.parse(data); // <-- dd/MM 24H format
      var outputFormat = DateFormat('dd MMM yyyy');
      outputDate = outputFormat.format(inputDate);
    }
    return outputDate;
  }

  static int getDeviceType() {
    if (kIsWeb) return 0;
    if (Platform.isAndroid) {
      return 1;
    } else {
      return 2;
    }
  }

  static Future waitFor(int seconds) async {
    await Future.delayed(Duration(seconds: seconds));
  }

  static Future waitForMili(int mili) async {
    await Future.delayed(Duration(milliseconds: mili));
  }

  static List<dynamic> getReportList() {
    List<dynamic> reportList = [
      {"title": tr('spam'), 'value': tr('spam')},
      {"title": tr('false_info'), 'value': tr('false_info')},
      {
        "title": tr('bullying_or_harassment'),
        'value': tr('bullying_or_harassment')
      },
      {
        "title": tr('violence_or_dangerous_organizations'),
        'value': 'violence_or_dangerous_organizations'
      },
      {
        "title": tr('hate_speech_of_symbols'),
        'value': tr('hate_speech_of_symbols')
      },
      {
        "title": tr('nudity_or_sexual_activity'),
        'value': tr('nudity_or_sexual_activity')
      },
      {"title": tr('scam_and_fraud'), 'value': tr('scam_and_fraud')},
    ];
    return reportList;
  }

  static String ordinalDate({String? dateVal}) {
    if (dateVal == null || dateVal.isEmpty) return '';
    try {
      var formatter = DateFormat('yyy MMMM dd');
      DateTime date = DateTime.parse(dateVal);
      List<String> dateList = formatter.format(date).split(' ');
      int i = int.tryParse(dateList[2]) ?? 0;
      if (i == 0) return '';
      if (i == 21) return "${i}st ${dateList[1]}";
      var j = i % 10, k = i % 100;
      if (j == 1 && k != 11) {
        return "${i}st";
      }
      if (j == 2 && k != 12) {
        return "${i}nd";
      }
      if (j == 3 && k != 13) {
        return "${i}rd";
      }
      return "${i}th ${dateList[1]}";
    } catch (e) {
      return '';
    }
  }

  static String ordinal(int n) {
    int i = n;
    var j = i % 10, k = i % 100;
    if (j == 1 && k != 11) {
      return "${i}st";
    }
    if (j == 2 && k != 12) {
      return "${i}nd";
    }
    if (j == 3 && k != 13) {
      return "${i}rd";
    }
    return "${i}th";
  }

  static int? getCategoryValue(String type) {
    if (UserSession.categoryData == null || UserSession.categoryData!.isEmpty) {
      return 0;
    }

    try {
      // Validate if the string is valid JSON
      final jsonData = json.decode(UserSession.categoryData!);
      CategoryResp respone = CategoryResp.fromJson(jsonData);

      for (var element in respone.data!.listData!) {
        if (element.title!.toLowerCase().contains(type.toLowerCase())) {
          return element.id;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        log('Error parsing category data: $e',
            name: 'Utility.getCategoryValue');
      }
    }
    return 0;
  }

  static String decryption(String plainText) {
    final key = enc.Key.fromUtf8('cG8C98qAUSvhufUs');

    final iv = IV.fromUtf8('0123456789012345');

    final encrypter = Encrypter(AES(key, mode: AESMode.ctr));
    final decrypted = encrypter.decrypt(Encrypted.from64(plainText), iv: iv);
    return decrypted;
  }

  String encrypted128(String textToEncrypt) {
    try {
      final key =
          enc.Key.fromUtf8('cG8C98qAUSvhufUsWEsdRTghTYuiHuIg'); // 32 length
      final iv = enc.IV.fromUtf8('0123456789012345');

      final encrypter =
          enc.Encrypter(enc.AES(key, mode: enc.AESMode.ctr, padding: null));
      final encrypted = encrypter.encrypt(textToEncrypt, iv: iv);

      return encrypted.base64;
    } catch (e) {
      if (kDebugMode) {
        log('Exception while encrypting: $e', name: 'Utility.encrypted128');
      }
      return textToEncrypt;
    }
  }

  //256
  String decrypted128(String encrypted) {
    try {
      String decryptedValue = "";
      final key =
          enc.Key.fromUtf8('cG8C98qAUSvhufUsWEsdRTghTYuiHuIg'); //32 length
      final iv = enc.IV.fromUtf8('0123456789012345');

      if (encrypted != 'null' && encrypted.isNotEmpty) {
        final decryptStr =
            enc.Encrypter(enc.AES(key, mode: enc.AESMode.ctr, padding: null));
        final decryptedBase64 = decryptStr
            .decryptBytes(enc.Encrypted.fromBase64(encrypted), iv: iv);
        decryptedValue = utf8.decode(decryptedBase64);
      }
      return decryptedValue;
    } catch (e) {
      if (kDebugMode) {
        log('Exception while decrypt $encrypted');
      }
      return encrypted;
    }
  }

  static String getRandomString(int length) {
    const chars =
        'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
    math.Random rnd = math.Random();
    return String.fromCharCodes(Iterable.generate(
        length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));
  }

  // Future<String> s3UploadFile(String? filePath) async {
  //   String? result;
  //   try {
  //     result = await AwsS3.uploadFile(
  //         // accessKey: "********************",
  //         accessKey: "********************",
  //         secretKey: "3p8v+6ADaoaXsBN1SldMnh1uWPbKErpNO/EPDLsk",
  //         file: File(filePath!),
  //         bucket: "nlms-cdn",
  //         region: "ap-south-1",
  //         // destDir: 'fd',
  //         // contentType: 'video/mp4',
  //         metadata: {"test": "test"});
  //     log("file uploaded to s3:: $filePath\nUploaded path $result");
  //   } on PlatformException {
  //     log("file uploading error utility.dart");
  //   }
  //   return result ?? '';
  // }

  static String getTime({required int? timestamp, required String dateFormat}) {
    if (timestamp == null) return '';
    tz.initializeTimeZones();

    // Define the India time zone
    final indiaTimeZone = tz.getLocation('Asia/Kolkata');
    //log(
    //     "date time now is location $indiaTimeZone and ${Preference.getString('region')}");
    // Define the target time zone
    final targetTimeZone = tz.getLocation('${Preference.getString('region')}');
    //log("date time now is location $targetTimeZone");

    // Get the current time in India
    final indiaNow =
        tz.TZDateTime.fromMillisecondsSinceEpoch(indiaTimeZone, timestamp);
    // final indiaNow = tz.TZDateTime.now(indiaTimeZone);

    // Convert the India time to the target time Target Time:  16-Mar-20zone
    final targetTime = tz.TZDateTime.from(indiaNow, targetTimeZone);

    // Format the target time
    final formatter = DateFormat(dateFormat);
    // final formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    final formattedTargetTime = formatter.format(targetTime);

    //log('India Time: ${indiaNow.toString()}');
    //log(
    //     'Target Time: $formattedTargetTime and new $targetTimeZone and india ${indiaNow.toString()}');
    return formattedTargetTime;
  }

  static int getTimeFromString({required String time}) {
    DateTime dateTime = DateTime.parse(time);
    int timestamp = dateTime.millisecondsSinceEpoch;
    return timestamp;
  }

  static String formatOffset(int offsetMilliseconds) {
    Duration offset = Duration(milliseconds: offsetMilliseconds);

    String offsetSign = offset.isNegative ? '-' : '+';
    int hours = offset.inHours.abs();
    int minutes = (offset.inMinutes.abs() % 60).toInt();

    String formattedOffset =
        DateFormat('HH:mm').format(DateTime(0, 0, 0, hours, minutes));

    return 'UTC$offsetSign$formattedOffset';
  }

  static String getCurrentTimeInTimeZone(String location) {
    final timeZone = tz.getLocation(location);
    return formatOffset(
        timeZone.timeZone(DateTime.now().millisecondsSinceEpoch).offset);
  }

  // static Future<DateTime> dateTimeNow() async {
  //   final ntpTime = await NTP.now();
  //  log("Api time is ${ntpTime.millisecondsSinceEpoch}");
  //   return ntpTime;
  // }

  // static DateTime? _cachedDateTime;
  // static final bool _isFetching = false;

  // static Future<void> _waitForValue() async {
  //   // Wait until the cached value becomes available or 10 seconds have passed
  //   final startTime = DateTime.now();
  //   while (_cachedDateTime == null &&
  //       DateTime.now().difference(startTime).inSeconds < 10) {
  //     await Future.delayed(Duration(milliseconds: 100));
  //   }
  // }

  // static void _startTimer() {
  //   Timer(Duration(seconds: 10), () {
  //     // Clear the cache after 10 seconds
  //     _cachedDateTime = null;
  //   });
  // }

  static int getIndiaTime() {
    int? indiaTimeDiff = Preference.getInt(Preference.timestampDiffToIndia);

    if (indiaTimeDiff != null) {
      return (indiaTimeDiff - (DateTime.now().millisecondsSinceEpoch ~/ 1000))
          .abs();
    }
    return 0;
  }

  static Future<int> strtotime(String? time) async {
    // return Future.value(1);
    // int? indiaTimeDiff = Preference.getInt(Preference.timestampDiffToIndia);

    // if (indiaTimeDiff != null) {
    //   return (indiaTimeDiff - (DateTime.now().millisecondsSinceEpoch ~/ 1000))
    //       .abs();
    // }

    //log(
    //     'the value of time is time diff diff $time $timeDiff and ${DateTime.parse('$time').millisecondsSinceEpoch ~/ 1000}');
    // return 1707503400 + timeDiff;

    //  i - c = diff ~/ 1000
    // i - o = diff

// flutter: the value of time is time diff diff 2024-02-10 00:00:00 0 and 1707503400
// d = c - i
    // return DateTime.parse('$time').millisecondsSinceEpoch ~/ 1000 - timeDiff;

    try {
      Map<String, dynamic> data = {};
      data['date_time'] = time;

      final dio = dio_pack.Dio();

      final response = await dio.post(
          APK_DETAILS['domain_url']! + ApiConstants.STR_TO_DATE,
          data: dio_pack.FormData.fromMap(data));

      if (response.statusCode == 200 || response.statusCode == 201) {
        TimeStampResponse data = TimeStampResponse.fromJson(response.data);
        // int timeDiff = Preference.getInt(Preference.timestampDiffToIndia) ?? 0;

        // log("the value of time is ${data.data!.list!.timeStamp!} and Current is ${DateTime.parse('$time').millisecondsSinceEpoch ~/ 1000} and time diff $timeDiff");
        log('Time******  ${data.data!.list!.timeStamp!}', name: 'Time******');
        return data.data!.list!.timeStamp!;
      }
    } catch (e, stacktrace) {
      if (kDebugMode) {
        log('response is exception $e and $stacktrace');
      }
    }
    int timeDiff = Preference.getInt(Preference.timestampDiffToIndia) ?? 0;
    Log.v('time diff is$timeDiff');
    return Future.value(int.tryParse(
        '${(DateTime.parse('$time').millisecondsSinceEpoch ~/ 1000) + (timeDiff ~/ 1000)}'));
  }

  Future<String> setupTimeZone() async {
    tz.initializeTimeZones();
    //var detroit = tz.getLocation('Asia/Muscat');
    //var timeInUtc = DateTime.utc(2023, 05, 31);
    //var timeZone = detroit.timeZone(timeInUtc.millisecondsSinceEpoch);

    /*var locations = tz.timeZoneDatabase.locations;
  log('TimeZone Muscat:');
  log(locations);
  log(locations.length);
  log(locations.keys.first);
  log(locations.keys.last);*/
    //print('TimeZone Muscat: ${timeZone}');
    //print('TimeZone Muscat: ${timeZone.isDst}');

    final DateTime now = DateTime.now();
    final pacificTimeZone = tz.getLocation('Asia/Muscat');
    var timeTT = tz.TZDateTime.from(now, pacificTimeZone);
    if (kDebugMode) {
      log('TimeZone Muscat: $timeTT');
    }

    return 'timeTT';
  }

  static Future<DateTime> getIndianDateNow({String? key}) async {
    // return await dateTimeNow();
    tz.initializeTimeZones();
    tz.TZDateTime now = tz.TZDateTime.now(tz.getLocation('Asia/Kolkata'));

    String formattedDate = DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(now);
    DateTime formattedDateTime = DateTime.parse(formattedDate);

    if (kDebugMode) {
      log("Current India Tim:  Request Key: $key  ${currentIndiaTime ?? formattedDateTime}");
    }
    return currentIndiaTime ?? formattedDateTime;
  }

  static Future<void> checkAndRequestCameraPermission() async {
    PermissionStatus status = await Permission.camera.status;
    if (status.isDenied) {
      // Permission has been denied previously
      bool permanentlyDenied = await Permission.camera.isPermanentlyDenied;
      if (permanentlyDenied) {
        // Permission permanently denied, navigate to app settings
        if (kDebugMode) {
          log('Camera permission request open camera');
        }
        openAppSettings();
      } else {
        // Permission denied, request again
        if (kDebugMode) {
          log('Camera permission request camera tesst');
        }
        requestCameraPermission();
      }
    } else if (status.isGranted) {
      // Permission has already been granted
      // Proceed with using the camera
      if (kDebugMode) {
        log('Camera permission granted!');
      }
    }
  }

  static Future<void> requestCameraPermission() async {
    PermissionStatus status = await Permission.camera.request();
    if (status.isGranted) {
      // Permission granted
      // Proceed with using the camera
      if (kDebugMode) {
        log('Camera permission granted!');
      }
    } else if (status.isPermanentlyDenied) {
      // Permission permanently denied, navigate to app settings
      openAppSettings();
    } else {
      openAppSettings();
      if (kDebugMode) {
        log('Camera permission denied! $status');
      }
    }
  }

  static Future<String> shortLink(String url) async {
    String apiUrl = 'https://sh.edulyst.live/short-url/';
    try {
      Log.v("response is hit ");
      http.Response response = await http.post(
        Uri.parse(apiUrl),
        body: {'original_url': url},
      );
      Log.v("response isnow ${response.body}");
      if (response.statusCode == 200) {
        // Success, handle the response
        Log.v("response is ${response.body}");

        return jsonDecode(response.body)['short_url'];
      } else {
        // Error, handle the response
        Log.v("response is ${response.body}");
      }
    } catch (error) {
      // Handle any errors that occur during the request
      Log.v("response is error $error");
    }
    return url;
  }

  Future<ShareResult?>? shareFile(BuildContext context,
      {required String url, String? text}) async {
    final ShareFileController controller = Get.put(ShareFileController());

    final dio_pack.Dio dio = dio_pack.Dio();

    // Get the application directory path to save the downloaded file
    // Directory appDocDir = await getApplicationDocumentsDirectory();
    String fileName = url.split('/').last;
    String? filePath;

    // DeviceInfoPlugin plugin = DeviceInfoPlugin();

    // late AndroidDeviceInfo android;
    try {
      // android = await plugin.androidInfo;

      if (Platform.isIOS) {
        var dir = await getApplicationDocumentsDirectory();

        filePath = path.join(
            dir.path, "${url.split("/").last.split(".").first} - $fileName");
      } else {
        final path = (await getExternalStorageDirectories(
                type: StorageDirectory.downloads))!
            .first;

        String localPath = path.path;

        filePath = "$localPath/$fileName";
      }
    } catch (e) {
      Log.v("exception file download $e");
    }

    //save file with progress
    SnackbarController barControlleer = Get.rawSnackbar(
      duration: Duration(days: 1),
      isDismissible: true,
      margin: const EdgeInsets.all(0),
      padding: const EdgeInsets.all(0),
      messageText: Obx(() => LinearProgressIndicator(
            value: controller.counter.value,
            minHeight: 5.0,
            backgroundColor: Colors.transparent,
            valueColor:
                AlwaysStoppedAnimation<Color>(context.appColors.gradientRight),
          )),
    );
    try {
      final response =
          await dio.download(url, filePath, onReceiveProgress: (r, t) {
        if (kDebugMode) {
          log("hello $r and $t");
        }
        controller.change(r / t);

        if (r / t == 1.0) barControlleer.close();
      });

      if (response.statusCode == 200) {
        XFile xFile = XFile(filePath!);
        try {
          return Share.shareXFiles([xFile], text: text);
        } catch (e) {
          printError(info: 'error $e');
        }
      }
      Log.v(filePath);
    } catch (ex) {
      Log.v(ex);
    }

    // Convert the file to an XFile

    return null;
  }

  static Future<List<int?>?>? getSize(File file) async {
    List<int> result = [];
    final videoExtensions = ['.mp4', '.avi', '.mkv', '.mov'];

    String extension =
        file.path.toLowerCase().substring(file.path.lastIndexOf('.'));

    if (videoExtensions.contains(extension)) {
      VideoPlayerController controller = VideoPlayerController.file(file);

      await controller.initialize(); // Wait for controller to initialize

      result = [
        controller.value.size.width.toInt(),
        controller.value.size.height.toInt()
      ];
      controller.dispose(); // Dispose the controller when done
    } else {
      Image image = Image.file(file);
      Completer<ui.Image> completer = Completer<ui.Image>();
      image.image
          .resolve(ImageConfiguration())
          .addListener(ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(info.image);
      }));
      await completer.future.then((value) {
        result = [value.width, value.height];
      });
    }
    log("file size is $result");
    return result;
  }

  static Future<bool> checkDirectoryExists(String directoryPath) async {
    try {
      final Directory directory = Directory(directoryPath);
      return await directory.exists();
    } catch (e) {
      // Handle exceptions if necessary
      return false;
    }
  }

  static Future<void> deleteFile(File file) async {
    try {
      if (await file.exists()) {
        await file.delete();
        Log.v(
          "File deleted: ${file.path}",
        );
      }
    } catch (e) {
      // Error in getting access to the file.
      Log.v("Error while deleting file: $e");
    }
  }

  /// code app link path
  /// eg. /g-carvaan/2345 => [g-carvaan, 2345]
  static List<String> decodePath(String s) {
    s = s.split('//')[1];
    return [s.split('/')[1], s.split('/').last];
  }

  static Future<void> logoutUser(BuildContext context) async {
    int? lanuageId = Preference.getInt(Preference.APP_LANGUAGE) ?? 1;
    String? appEnglishName =
        Preference.getString(Preference.APP_ENGLISH_NAME) ?? 'en';
    String email = Preference.getString(Preference.LOGIN_ID) ?? '';
    String password = Preference.getString(Preference.LOGIN_PASS) ?? '';
    bool rememberMe = Preference.getBool(Preference.rememberMe) ?? false;
    UserSession.clearSession();
    context.read<ThemeBloc>().add(SetLightThemeEvent());
    // await Hive.deleteFromDisk();
    await Preference.clearPref().then((value) async {
      Preference.setString(Preference.LOGIN_ID, email);
      Preference.setString(Preference.LOGIN_PASS, password);
      Preference.setInt(Preference.APP_LANGUAGE, lanuageId);
      Preference.setBool(Preference.rememberMe, rememberMe);

      Preference.setString(Preference.APP_ENGLISH_NAME, appEnglishName);

      if (APK_DETAILS["package_name"] == "com.singulariswow" ||
          APK_DETAILS["package_name"] == "com.singularis.jumeira") {
        Navigator.pushAndRemoveUntil(context,
            NextPageRoute(SingularisWowPreBoarding()), (route) => false);
      } else {
        Navigator.pushAndRemoveUntil(
            context, NextPageRoute(FullScreenPreboarding()), (route) => false);
      }

      Navigator.pushAndRemoveUntil(
          context, NextPageRoute(SingularisWowPreBoarding()), (route) => false);
    });
  }

  static Future<void> clearLocalUserData(BuildContext context) async {
    Preference.setString(Preference.USER_TOKEN, '');
  }

  static bool isVideoUrl(String url) {
    // List of common video file extensions
    final List<String> videoExtensions = [
      '.mp4',
      '.mov',
      '.wmv',
      '.flv',
      '.avi',
      '.mkv',
      '.webm',
      '.mpeg',
      '.mpg'
    ];

    // Convert URL to lowercase to handle case-insensitive comparison
    String lowerCaseUrl = url.toLowerCase();

    // Check if the URL contains any of the video extensions
    for (String extension in videoExtensions) {
      if (lowerCaseUrl.contains(extension)) {
        return true;
      }
    }

    return false;
  }
}

class ShareFileController extends GetxController {
  // Declare the reactive variable with Rx
  RxDouble counter = 0.0.obs;

  // Function to increment the counter
  void change(double value) {
    counter.value = value;
  }
}
