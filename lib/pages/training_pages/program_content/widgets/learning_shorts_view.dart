import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/pages/training_pages/program_content/widgets/text_view.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:page_transition/page_transition.dart';

import 'learning_short_video_player.dart';
import 'learning_shorts_content_player.dart';

class LearningShortsView extends StatelessWidget {
  final LearningShots learningShorts;
  const LearningShortsView({super.key, required this.learningShorts});

  @override
  Widget build(BuildContext context) {
    return learningShorts.contentType?.toLowerCase() == 'notes'
        ? Container(
            color: context.appColors.surface,
            width: double.infinity,
            height: MediaQuery.of(context).size.height * 0.24,
            margin: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            child: Stack(
              children: [
                Image.asset(
                  'assets/images/note_bg.png',
                  width: width(context),
                  fit: BoxFit.cover,
                ),
                Positioned(
                  bottom: 10,
                  left: 0,
                  right: 0,
                  child: InkWell(
                    onTap: () {
                      openNotes(context, isNoteView: true);
                    },
                    child: Container(
                        width: width(context),
                        height: 38,
                        margin: const EdgeInsets.symmetric(
                            horizontal: 18, vertical: 4),
                        decoration: BoxDecoration(
                            gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                  context.appColors.gradientLeft,
                                  context.appColors.gradientRight
                                ]),
                            borderRadius: BorderRadius.circular(8)),
                        child: Center(
                          child: Text(
                            'view_note',
                            style: Styles.regular(
                                size: 14,
                                color: context.appColors.primaryForeground),
                            textAlign: TextAlign.center,
                          ).tr(),
                        )),
                  ),
                )
              ],
            ),
          )
        : learningShorts.contentType?.toLowerCase() == 'text'
            ? TextView(learningShorts: learningShorts)
            : LearningShortVideoPlayer(
                learningShorts: learningShorts,
              );
  }

  void openNotes(BuildContext context, {required bool isNoteView}) {
    log('learning shoorts ${learningShorts.url}');
    Navigator.push(
        context,
        PageTransition(
            type: PageTransitionType.fade,
            child: LearningShortsContentPlayer(
              isNoteView: isNoteView,
              learningShorts: learningShorts,
            )));
  }
}
