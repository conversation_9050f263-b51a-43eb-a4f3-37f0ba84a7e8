// ignore_for_file: unused_field

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class CommonHTMLWebView extends StatefulWidget {
  final String? url;
  final String? title;
  bool _isLoading = false;
  bool isLandScape, isLocal, fullScreen;
  final bool addHtmlTag;

  CommonHTMLWebView(
      {this.url,
      Key? key,
      this.isLandScape = true,
      this.isLocal = false,
      this.fullScreen = false,
      this.addHtmlTag = false,
      required this.title})
      : super(key: key);

  @override
  _CommonHTMLWebViewState createState() => _CommonHTMLWebViewState();
}

class _CommonHTMLWebViewState extends State<CommonHTMLWebView> {
  InAppWebViewController? _webViewController;
  String url = "";
  double progress = 0;

  String htmlViewTest = '';

  @override
  void initState() {
    htmlViewTest = '''<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
   
    <style>
        .highlight-text {
            color: red;
            background-color: green;
        }
        .table-1 {
            width: 100%;
            border-collapse: collapse;
        }
        .table-1 td {
            border: 1px solid black;
            padding: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
${widget.url!}
</body>
</html>
''';

    super.initState();
    if (widget.isLandScape)
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeRight,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.portraitDown,
        DeviceOrientation.portraitUp,
      ]);
    if (widget.fullScreen)
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitDown,
      DeviceOrientation.portraitUp,
    ]);
    super.dispose();
  }

  String formatUrl(String url) {
    if (url.startsWith('www.')) {
      url = url.replaceFirst('www.', 'https://');
    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    return url;
  }

  double zoomScale = 10.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.background,
      extendBodyBehindAppBar: false,
      appBar: AppBar(
          title: Text(widget.title!,
              style: Styles.getBoldThemeStyle(context, size: 14)),
          elevation: 0.5,
          leading: BackButton(color: context.appColors.textBlack),
          backgroundColor: context.appColors.surface),
      body: ScreenWithLoader(
        isLoading: widget._isLoading,
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: InAppWebView(
            onZoomScaleChanged: (controller, oldScale, newScale) {
              setState(() {
                zoomScale = newScale;
              });
            },
            //initialData: InAppWebViewInitialData(data: widget.url!),
            initialData: widget.addHtmlTag == true
                ? InAppWebViewInitialData(data: htmlViewTest)
                : InAppWebViewInitialData(data: widget.url!),

            //     URLRequest(url: Uri.parse(formatUrl(widget.url!))),
            initialSettings: InAppWebViewSettings(
              preferredContentMode: UserPreferredContentMode.RECOMMENDED,
              minimumFontSize: 100,
              incognito: true,
              javaScriptEnabled: true,
              cacheEnabled: true,
              supportZoom: true,
              userAgent: "Chrome/90.0.4430.51",
              useOnLoadResource: true,
              mediaPlaybackRequiresUserGesture: false,
            ),
            onWebViewCreated: (InAppWebViewController controller) {
              // _webViewController = controller;
            },
            onPermissionRequest: (InAppWebViewController controller,
                PermissionRequest permissionRequest) async {
              return PermissionResponse(
                  resources: permissionRequest.resources,
                  action: PermissionResponseAction.GRANT);
            },

            onLoadStart: (InAppWebViewController controller, Uri? url) async {
              // if(controller.)
              if (url.toString().contains('g-home')) {
                Navigator.pop(context, true);
              }
              setState(() {
                this.url = url.toString();
              });
            },
            onLoadStop: (InAppWebViewController controller, Uri? url) async {
              setState(() {
                this.url = url.toString();
              });
            },
            onReceivedError: (InAppWebViewController c, WebResourceRequest r,
                WebResourceError e) {},
            onConsoleMessage: (controller, consoleMessage) {},
            onProgressChanged:
                (InAppWebViewController controller, int progress) {
              setState(() {
                this.progress = progress / 100;
                progress == 100
                    ? widget._isLoading = false
                    : widget._isLoading = true;
              });
            },

            onLoadResource: (controller, resource) {
              widget._isLoading = false;
            },
            onReceivedServerTrustAuthRequest: (controller, challenge) async {
              return ServerTrustAuthResponse(
                  action: ServerTrustAuthResponseAction.PROCEED);
            },
            shouldOverrideUrlLoading: (controller, navigationAction) async {
              var uri = navigationAction.request.url!;

              if (uri.host == 'google.com') {
                // Handle specific URLs or conditions here
                return NavigationActionPolicy.ALLOW;
              }

              return NavigationActionPolicy.CANCEL;
            },
          ),
        ),
      ),
    );
  }
}
