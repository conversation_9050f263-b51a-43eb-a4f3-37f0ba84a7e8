import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CommonWebviewPage extends StatefulWidget {
  final String? strUrl;
  const CommonWebviewPage({this.strUrl, super.key});

  @override
  State<CommonWebviewPage> createState() => _CommonWebviewPageState();
}

class _CommonWebviewPageState extends State<CommonWebviewPage> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      debugPrint('strUrl:--- ${widget.strUrl}');
    }
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.strUrl ?? 'about:blank'));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('view').tr()),
      body: widget.strUrl == null
          ? Center(child: Text('No URL provided').tr())
          : WebViewWidget(controller: _controller),
    );
  }
}
