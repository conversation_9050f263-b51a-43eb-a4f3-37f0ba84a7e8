import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/notification_list_resp.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/notifications/notification_view.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';

import 'package:masterg/utils/extensions/sizedbox_extension.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class NotificationsListItem extends StatefulWidget {
  final List<Data>? allNotifications;
  final Function(String?, int?, String?, String?, int) onTapCallback;

  NotificationsListItem({
    Key? key,
    required this.allNotifications,
    required this.onTapCallback,
  }) : super(key: key);

  @override
  State<NotificationsListItem> createState() => _NotificationsListItemState();
}

class _NotificationsListItemState extends State<NotificationsListItem> {
  late ScrollController _scrollController;
  late List<Data>? allNotificationsLazy;
  bool _isLoadingLazy = false;
  bool isLoadingMore = false;
  int from = 10;
  int to = 10;

  @override
  void initState() {
    _scrollController = ScrollController()..addListener(_scrollListener);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    super.dispose();
  }

  void _scrollListener() {
    if (_isLoadingLazy) return;
    if (_scrollController.position.extentAfter == 0.0) {
      _isLoadingLazy = true;
      getNotificationList();
    }
  }

  @override
  Widget build(BuildContext context) {
    String extractDateDetails(String dateString) {
      DateTime date = DateTime.parse(dateString);
      String dayName = DateFormat('EE').format(date);
      String monthName = DateFormat('MMM').format(date);
      String day = DateFormat('d').format(date);
      // String year = DateFormat('y').format(date);
      String time = DateFormat('h:mm a').format(date);
      return '$day $monthName $dayName $time';
    }

    // Sort the notifications based on createdAt field
    // List<Data>? sortedNotifications =
    List.from(widget.allNotifications!)
      ..sort((a, b) {
        DateTime aDate = DateTime.parse(a.createdAt!);
        DateTime bDate = DateTime.parse(b.createdAt!);
        return bDate.compareTo(aDate);
      });

    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) {
        if (state is NotificationsListState) {
          _handleNotificationListState(state);
        }
      },
      child: Container(
        height: MediaQuery.of(context).size.height,
        padding: EdgeInsets.all(8.0),
        child: ListView.builder(
          controller: _scrollController,
          shrinkWrap: true,
          physics: BouncingScrollPhysics(),
          itemCount: widget.allNotifications?.length,
          itemBuilder: (context, index) {
            // Data notification = sortedNotifications[index];
            // ignore: unrelated_type_equality_checks
            String? readContent =
                widget.allNotifications?[index].readContent?.toLowerCase();
            bool isRead = readContent == 'is_read';

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                InkWell(
                  onTap: () {
                    setState(() {
                      widget.allNotifications?[index].readContent = 'is_read';
                    });
                    if (!isRead) {
                      widget.onTapCallback(
                          '${widget.allNotifications?[index].id}',
                          widget.allNotifications?[index].notifiableId,
                          '${widget.allNotifications?[index].type}',
                          '${widget.allNotifications?[index].readContent}',
                          index);
                    }
                    Navigator.push(
                      context,
                      NextPageRoute(
                          NotificationViewPage(
                            notificationId:
                                '${widget.allNotifications?[index].id ?? ''}',
                            notiTitle:
                                '${widget.allNotifications?[index].title ?? ''}',
                            notiDesc:
                                '${widget.allNotifications?[index].description ?? ''}',
                            dateTime:
                                '${extractDateDetails('${widget.allNotifications?[index].createdAt}')}',
                            type:
                                '${widget.allNotifications?[index].type ?? ''}',
                            route:
                                '${widget.allNotifications?[index].dataDetails?.route ?? ''}',
                            id: widget
                                    .allNotifications?[index].dataDetails?.id ??
                                0,
                          ),
                          isMaintainState: true),
                    );
                  },
                  child: Container(
                      //height: 72,
                      padding: EdgeInsets.all(0),
                      margin: EdgeInsets.symmetric(vertical: 5.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: context.appColors.grey),
                        borderRadius: BorderRadius.circular(4.0),
                        color: isRead
                            ? context.appColors.surface
                            : context.appColors.gradientRight
                                .withValues(alpha: 0.08),
                      ),
                      child: Row(
                        children: [
                          _buildColumn(
                              color: isRead
                                  ? context.appColors.textWhite
                                  : context.appColors.gradientRight
                                      .withValues(alpha: 0.08),
                              status: widget.allNotifications?[index].type),
                          SizedBox(width: 5),
                          Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: SizedBox(
                              //height: 100,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width:
                                        MediaQuery.of(context).size.width - 120,
                                    child: Text(
                                      '${widget.allNotifications?[index].title ?? ''}',
                                      style: Styles.bold(
                                          color: isRead
                                              ? context.appColors.textBlack
                                              : context.appColors.headingText,
                                          size: 14),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width -
                                                120,
                                        child: widget.allNotifications?[index]
                                                    .description !=
                                                null
                                            ? (widget.allNotifications?[index]
                                                        .description
                                                        .contains('<') &&
                                                    widget
                                                        .allNotifications?[
                                                            index]
                                                        .description
                                                        ?.contains('>'))
                                                ? Html(
                                                    data:
                                                        """${widget.allNotifications?[index].description}""",
                                                  )
                                                : Text(
                                                    '${widget.allNotifications?[index].description ?? ''}',
                                                    softWrap: true,
                                                    maxLines: 3,
                                                    style: Styles.dMSansregular(
                                                        size: 12,
                                                        color: isRead
                                                            ? context.appColors
                                                                .textBlack
                                                            : context.appColors
                                                                .headingText),
                                                  )
                                            : SizedBox() /*
                                      
                                    : ,*/
                                        ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 10.0),
                                    child: Text(
                                        '${extractDateDetails('${widget.allNotifications?[index].createdAt ?? ''}')}',
                                        style: Styles.regular(
                                            color: isRead
                                                ? context.appColors.textBlack
                                                : context.appColors.headingText,
                                            size: 10)),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 5.0),
                                    child: Text(
                                        '${widget.allNotifications?[index].type ?? ''}',
                                        style: Styles.regular(
                                            color: isRead
                                                ? context.appColors.textBlack
                                                : context.appColors.headingText,
                                            size: 10)),
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      )),
                ),
                if (isLoadingMore &&
                    index == widget.allNotifications!.length - 1) ...[
                  20.height,
                  CupertinoActivityIndicator(),
                ]
              ],
            );
          },
        ),
      ),
    );
  }

  void getNotificationList() {
    BlocProvider.of<HomeBloc>(context)
        .add(NotificationsListEvent(fromValue: from, toValue: to));
  }

  void _handleNotificationListState(NotificationsListState state) {
    setState(() {
      switch (state.apiState) {
        case NotificationStatus.loading:
          Log.v("bbbSubscriptionCoursesState Loading.................");
          break;
        case NotificationStatus.loadingMore:
          isLoadingMore = true;
          Log.v("NotificationStatus Loading More.................");
          break;
        case NotificationStatus.success:
          try {
            if (state.response != null && state.response!.data != null) {
              // notificationList = state.response;
              from += 10;
              _isLoadingLazy = false;
              isLoadingMore = false;
              allNotificationsLazy!.addAll(state.response!.data!);
              widget.allNotifications?.addAll(allNotificationsLazy!);
            } else {}
          } catch (e, stackTrace) {
            debugPrint('$stackTrace');
          }
          break;

        case NotificationStatus.error:
          isLoadingMore = false;
          Log.v("SubscriptionCoursesState Error..........................");
          break;
        case NotificationStatus.initial:
          break;
      }
    });
  }

  Widget _buildColumn({Color? color, String? status}) {
    return Container(
      height: 100,
      width: 70,
      decoration: BoxDecoration(color: color),
      child: status?.toLowerCase() == 'assessment'
          ? Icon(Icons.quiz_outlined, size: 30)
          : status?.toLowerCase() == 'video_yts'
              ? Icon(Icons.live_tv, size: 30)
              : status?.toLowerCase() == 'video'
                  ? Icon(Icons.video_call, size: 30)
                  : Icon(Icons.message, size: 30),
    );
  }
}
