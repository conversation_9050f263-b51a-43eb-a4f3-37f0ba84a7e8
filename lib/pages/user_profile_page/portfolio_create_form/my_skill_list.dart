import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/skill_rating_resp.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_skill.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:page_transition/page_transition.dart';

class MySkillList extends StatefulWidget {
  final SkillRatingResponse? skillResponse;
  const MySkillList({super.key, this.skillResponse});

  @override
  State<MySkillList> createState() => _MySkillListState();
}

class _MySkillListState extends State<MySkillList> {
  SkillRatingResponse? skillResponse;
  bool isSkillRatingLoading = false;

  bool isSkillDelete = false;

  @override
  void initState() {
    skillResponse = widget.skillResponse;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            iconTheme: IconThemeData(color: context.appColors.textBlack),
            backgroundColor: context.appColors.surface,
            elevation: 0.1,
            centerTitle: false,
            actions: [
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: () {
                  Navigator.push(
                          context,
                          PageTransition(
                              duration: Duration(milliseconds: 350),
                              reverseDuration: Duration(milliseconds: 350),
                              type: PageTransitionType.bottomToTop,
                              child: AddSkill()))
                      .then((value) => skillRating());
                },
              )
            ],
            title: Text(
              'skill_and_rating',
              style: Styles.getSemiboldThemeStyle(context, size: 16),
            ).tr()),
        body: ScreenWithLoader(
            isLoading: isSkillRatingLoading,
            body: BlocManager(
              initState: (value) {},
              child: BlocListener<HomeBloc, HomeState>(
                listener: (context, state) async {
                  if (state is DeleteSkillState) {
                    handleDeleteSkill(state);
                  }
                  if (state is SkillRatingState) handleSkillRating(state);
                },
                child: ListView.builder(
                  padding: const EdgeInsets.only(top: 12),
                  physics: NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.vertical,
                  shrinkWrap: true,
                  itemCount: skillResponse?.data?.length ?? 0,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.fromLTRB(12, 5, 12, 5),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: width(context) * 0.76,
                            child: CustomRating(
                                progressWidth: width(context) * 0.76,
                                title: '${skillResponse?.data?[index].name}',
                                percentage: int.parse(
                                    '${skillResponse?.data?[index].selfProficiency ?? '0'}'),
                                onClick: () {}),
                          ),
                          SizedBox(width: width(context) * 0.02),
                          InkWell(
                            onTap: () async {
                              await Navigator.push(
                                  context,
                                  PageTransition(
                                      duration: Duration(milliseconds: 350),
                                      reverseDuration:
                                          Duration(milliseconds: 350),
                                      type: PageTransitionType.bottomToTop,
                                      child: AddSkill(
                                        isEditMode: true,
                                        skill: skillResponse?.data?[index],
                                      ))).then((value) => skillRating());
                            },
                            child: Transform.scale(
                                scale: 1.2,
                                child: SvgPicture.asset(
                                  'assets/images/edit_portfolio.svg',
                                  colorFilter: context.isDarkMode
                                      ? ColorFilter.mode(
                                          context.appColors.primaryForeground,
                                          BlendMode.srcIn)
                                      : null,
                                  color: context.appColors.dashboardApplyColor,
                                )),
                          ),
                          SizedBox(width: width(context) * 0.03),
                          Transform.scale(
                              scale: 1.2,
                              child: InkWell(
                                onTap: () async {
                                  AlertsWidget.showCustomDialog(
                                      context: context,
                                      title: '',
                                      text: tr('confirm_deletion_textone'),
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      onOkClick: () async {
                                        getDeleteSkill(
                                            skillResponse?.data?[index].id);
                                      });
                                },
                                child: SvgPicture.asset(
                                  'assets/images/delete.svg',
                                  color: context.appColors.dashboardApplyColor,
                                ),
                              )),
                        ],
                      ),
                    );
                  },
                ),
              ),
            )));
  }

  void getDeleteSkill(int? skillId) {
    BlocProvider.of<HomeBloc>(context).add(DeleteSkillEvent(skillId: skillId));
  }

  void handleDeleteSkill(DeleteSkillState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add  Skills....................");
          isSkillDelete = true;

          break;

        case ApiStatus.SUCCESS:
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${state.response?.data?.list}',
                style: Styles.regular(color: context.appColors.textWhite)),
          ));
          skillRating();
          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Skills....................");
          isSkillDelete = false;
          FirebaseAnalytics.instance.logEvent(name: 'my_skill', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void skillRating() {
    BlocProvider.of<HomeBloc>(context).add(SkillRatingEvent());
  }

  void handleSkillRating(SkillRatingState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Skill List....................");
          isSkillRatingLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Skill List....................");
          skillResponse = state.response;
          isSkillRatingLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v("Error  Skill List....................");
          isSkillRatingLoading = false;
          FirebaseAnalytics.instance.logEvent(name: 'my_skill', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
