import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:masterg/utils/extensions/snackbar_extenstion.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:hive/hive.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

import '../../../../utils/config.dart';
import '../../../../utils/offline_data/url_model.dart';

class CompetitionNotes extends StatefulWidget {
  final int? id;
  final notesUrl;
  final String? title;
  const CompetitionNotes({super.key, this.notesUrl, this.id, this.title});

  @override
  State<CompetitionNotes> createState() => _CompetitionNotesState();
}

class _CompetitionNotesState extends State<CompetitionNotes> {
  Dio? dio;

  void _updateCourseCompletion(bookmark, int completionPercent) async {
    //change bookmark with 25
    try {
      BlocProvider.of<HomeBloc>(context).add(UpdateVideoCompletionEvent(
          bookmark: bookmark,
          contentId: widget.id,
          completionPercent: completionPercent));
    } catch (e) {
      Log.v('exception is $e');
    }
    setState(() {});
  }

  bool isLoading = false;
  double progress = 0.0;

  @override
  void initState() {
    super.initState();
  }

  Future<void> downloadFile(
    String url,
    int postid,
  ) async {
    try {
      setState(() {
        isLoading = true;
        progress = 0.0;
      });

      log('ssssssss: $url');
      // Get a temporary directory
      final dir = await getTemporaryDirectory();
      //final filePath = "${dir.path}/project_manager_resume.pdf";
      final filePath = "${dir.path}/learn$postid.pdf";

      // Download the file
      await Dio().download(
        url,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() {
              progress = received / total;
            });
          }
        },
      );

      log('FilePath:--------$filePath');
      // Open the file
      //await OpenFilex.open(filePath);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Error downloading file: $e")),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  //TODO: Offline pdf Download Function
  Future<void> _downloadAndSaveVideo({
    required String PdfUrl,
    required String videoThumbnail,
    required String view,
    required String des,
    required int postid,
  }) async {
    try {
      log('videoURL====$PdfUrl');
      // String appDocDir;
      //Directory appDocDir = await getApplicationDocumentsDirectory();
      final dir = await getTemporaryDirectory();
      //final filePath = "${dir.path}/project_manager_resume.pdf";
      final filePath = "${dir.path}/learn$postid.pdf";

      await Dio().download(
        PdfUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() {
              progress = received / total;
            });
          }
        },
      );

      log('File downloaded to: $filePath');

      // Save the file path to Hive
      final box = await Hive.openBox<URLModel>('pdf_offline');

      bool isDuplicate = box.values.any((element) => element.postid == postid);
      if (isDuplicate) {
        if (mounted) {
          "PDF already exists!".showSnackbar(context);
        }
      } else {
        box.add(URLModel(filePath, videoThumbnail, view, des, postid));
        if (mounted) {
          "PDF added successfully!".showSnackbar(context);
        }
      }
    } catch (e) {
      log('Error downloading file: $e');
      if (mounted) {
        "Error downloading and saving PDF".showSnackbar(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            title: Text(widget.title ?? '',
                style: TextStyle(color: context.appColors.textBlack)),
            elevation: 0,
            leading: IconButton(
              onPressed: () => Navigator.pop(
                context,
              ),
              icon: Icon(
                Icons.arrow_back_ios,
                color: context.appColors.textBlack,
              ),
            ),
            actions: APK_DETAILS["offline_pdf_download_learn"] == "1"
                ? <Widget>[
                    IconButton(
                      onPressed: () {
                        _downloadAndSaveVideo(
                          PdfUrl: widget.notesUrl,
                          videoThumbnail: '',
                          view: 'pdf',
                          des: widget.title ?? '',
                          postid: widget.id!,
                        );

                        //downloadFile(widget.notesUrl, widget.id!);
                      },
                      icon: Icon(
                        Icons.cloud_download_rounded,
                        color: context.appColors.textBlack,
                      ),
                    )
                  ]
                : null,
            backgroundColor: context.appColors.surface),
        body: SizedBox(
          width: width(context),
          height: height(context),
          child: PDF(
            onViewCreated: ((controller) {
              controller.getPageCount().then((value) {
                if (value != null && value > 0) {
                  _updateCourseCompletion(1, ((1 / value) * 100).toInt());
                }
              });
            }),
            onPageChanged: ((page, total) {
              if (page != null && total != null && total > 0) {
                int pageno = page + 1;
                _updateCourseCompletion(
                    pageno, ((pageno / total) * 100).toInt());
              }
            }),
            enableSwipe: true,
            gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
              Factory<PanGestureRecognizer>(() => PanGestureRecognizer()),
              Factory<VerticalDragGestureRecognizer>(
                  () => VerticalDragGestureRecognizer())
            },
          ).cachedFromUrl(
            widget.notesUrl,
            placeholder: (progress) => Center(child: Text('$progress %')),
            errorWidget: (error) => Center(child: Text(error.toString())),
          ),
        ));
  }
}
