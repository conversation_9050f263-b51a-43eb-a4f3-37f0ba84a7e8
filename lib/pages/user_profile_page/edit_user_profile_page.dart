import 'dart:io';
import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/user_profile_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/custom_pages/faq_page.dart';
import 'package:masterg/pages/user_profile_page/mobile_ui_helper.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/click_picker.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:shimmer/shimmer.dart';

class EditUserProfilePage extends StatefulWidget {
  const EditUserProfilePage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _EditUserProfilePageState();
  }
}

class _EditUserProfilePageState extends State<EditUserProfilePage>
    with SingleTickerProviderStateMixin {
  Box? box;
  bool isLoading = false;

  UserProfileData? userProfileDataList;
  String? selectedImage;

  @override
  void initState() {
    super.initState();
    _getUserProfile();
  }

  void _getUserProfile() async {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context).add(GetUserProfileEvent());
  }

  void _updateUserProfileImage(String? filePath) async {
    BlocProvider.of<HomeBloc>(context)
        .add(UpdateUserProfileImageEvent(filePath: filePath));
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is GetUserProfileState) {
            _handleUserProfileResponse(state);
          }
          if (state is UpdateUserProfileImageState) {
            _handleUpdateUserProfileImageResponse(state);
          }
        },
        child: Scaffold(
          backgroundColor: Colors.grey[200],
          appBar: AppBar(
            elevation: 0,
            leading: BackButton(color: Colors.black),
            backgroundColor: context.appColors.primary,
            actions: [
              IconButton(
                  onPressed: () {
                    AlertsWidget.showCustomDialog(
                        context: context,
                        title: tr('leavingSoSoon'),
                        text: tr('areYouSureYouWantToExit'),
                        icon: 'assets/images/circle_alert_fill.svg',
                        onOkClick: () async {
                          Utility.logoutUser(context);
                        });
                  },
                  icon: Icon(
                    Icons.logout,
                    color: Colors.black,
                  ))
            ],
          ),
          body: userProfileDataList != null ? _makeBody() : BlankPage(),
        ),
      ),
    );
  }

  Widget _makeBody() {
    return Stack(
      children: [
        Container(
          height: 80,
          decoration: BoxDecoration(
              color: context.appColors.primary,
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12))),
          // child: HeaderWidget(100, false, Icons.house_rounded),
        ),
        Container(
          alignment: Alignment.center,
          margin: EdgeInsets.fromLTRB(0, 26, 0, 10),
          // padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
          child: Column(
            children: [
              Container(
                height: 100.0,
                width: 100.0,
                padding: EdgeInsets.all(2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  border: Border.all(width: 0, color: Colors.transparent),
                  color: context.appColors.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 100,
                      offset: const Offset(5, 5),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    selectedImage != null && selectedImage!.isNotEmpty
                        ? Container(
                            height: 100,
                            width: 100,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                image: FileImage(File('$selectedImage')),
                                fit: BoxFit.fill,
                              ),
                            ),
                            child: null /* add child content here */,
                          )
                        : userProfileDataList!.profileImage != null
                            ? ClipOval(
                                child: Image.network(
                                  userProfileDataList!.profileImage!,
                                  filterQuality: FilterQuality.low,
                                  width: 100,
                                  height: 100,
                                  fit: BoxFit.fill,
                                ),
                              )
                            : SvgPicture.asset(
                                'assets/images/bxs_user_circle.svg',
                                allowDrawingOutsideViewBox: true,
                              ),
                    Positioned(
                      left: 62,
                      top: 62,
                      child: InkWell(
                        onTap: () {
                          showBottomSheet(context);
                          /*Navigator.push(
                              context,
                              NextPageRoute(ChangeImage()));*/
                        },
                        child: Container(
                          height: 30.0,
                          width: 30.0,
                          padding: EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100),
                            border:
                                Border.all(width: 0, color: Colors.transparent),
                            color: Colors.grey[200],
                          ),
                          child: Icon(
                            Icons.edit,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${userProfileDataList!.name}',
                    style: Styles.bold(
                        color: context.appColors.textBlack, size: 20),
                  ),
                  /*SvgPicture.asset('assets/images/edit_profile_icon.svg',
                      width: 20, height: 20)*/
                ],
              ),

              // Text(
              //   userProfileDataList.organization,
              //   style: Styles.semibold(color: context.appColors.textBlack),
              // ),
              SizedBox(
                height: 10,
              ),
              Container(
                padding: EdgeInsets.all(10),
                color: context.appColors.surface,
                margin: EdgeInsets.fromLTRB(0, 0, 0, 20),
                child: Column(
                  children: <Widget>[
                    Container(
                      alignment: Alignment.topLeft,
                      padding: EdgeInsets.all(15),
                      child: Column(
                        children: <Widget>[
                          Column(
                            children: <Widget>[
                              ListTile(
                                title: Text('enter_email',
                                        style: Styles.regular(
                                            color: context.appColors.grey3,
                                            size: 12))
                                    .tr(),
                                subtitle: Text(
                                  '${userProfileDataList!.email}',
                                  style: Styles.regular(
                                      size: 16,
                                      color: context.appColors.textBlack),
                                ),
                              ),
                              ListTile(
                                title: Text('phone_no',
                                        style: Styles.regular(
                                            color: context.appColors.grey3,
                                            size: 12))
                                    .tr(),
                                subtitle: Text(
                                    '${userProfileDataList!.mobileNo}',
                                    style: Styles.regular(
                                        size: 16,
                                        color: context.appColors.textBlack)),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),

              Container(
                height: 100,
                color: context.appColors.surface,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: InkWell(
                        onTap: () {
                          Navigator.push(context, NextPageRoute(FaqPage()));
                        },
                        child: Row(
                          children: [
                            Container(
                                width: 30,
                                height: 30,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: context.appColors.primary,
                                ),
                                child: Icon(
                                  Icons.info,
                                  color: context.appColors.primaryForeground,
                                  size: 20,
                                )),
                            SizedBox(width: 10),
                            Text('FAQ',
                                style: Styles.getRegularThemeStyle(context)),
                            Expanded(child: SizedBox()),
                            Icon(Icons.arrow_forward_ios, size: 15),
                          ],
                        ),
                      ),
                    ),
                    Divider(
                      indent: 60,
                    )
                  ],
                ),
              ),

              Expanded(child: SizedBox()),
            ],
          ),
        )
      ],
    );
  }

  void _handleUserProfileResponse(GetUserProfileState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");
          Log.v(state.response!.data!.list.toString());

          userProfileDataList = state.response!.data!.list;
          Preference.setString(
              Preference.PROFILE_IMAGE, '${userProfileDataList!.profileImage}');

          isLoading = false;
          break;
        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'edit_user_profile', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleUpdateUserProfileImageResponse(
      UpdateUserProfileImageState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");

          Preference.setString(Preference.PROFILE_IMAGE,
              '${state.response!.data!.profileImage}');

          isLoading = false;
          break;
        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<String> _getImages(ImageSource source, String sourceType) async {
    if (sourceType == 'camera') {
      final picker = ImagePicker();
      XFile? pickedFile =
          await picker.pickImage(source: source, imageQuality: 100);
      if (pickedFile != null) {
        return pickedFile.path;
      }
      return "";
    } else {
      final picker = ImagePicker();
      XFile? pickedFile =
          await picker.pickImage(source: source, imageQuality: 100);
      if (pickedFile != null) return pickedFile.path;
      return "";
    }
  }

  Future<String> _cropImage(pickedFile) async {
    if (pickedFile != null) {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: pickedFile,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        uiSettings: buildUiSettings(context),
      );
      if (croppedFile != null) {
        return croppedFile.path;
      }
    }
    return "";
  }

  void showBottomSheet(BuildContext context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.black,
        builder: (context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Center(
                child: Container(
                  padding: EdgeInsets.all(10),
                  margin: EdgeInsets.only(top: 10),
                  height: 4,
                  width: 70,
                  decoration: BoxDecoration(
                      color: context.appColors.surface,
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
              ListTile(
                leading: Icon(
                  Icons.image,
                  color: context.appColors.primaryForeground,
                ),
                title: Text(
                  'gallery',
                  style: TextStyle(color: context.appColors.primaryForeground),
                ).tr(),
                onTap: () async {
                  await _getImages(ImageSource.gallery, 'gallery')
                      .then((value) async {
                    selectedImage = value;
                    selectedImage = await _cropImage(value);

                    if (selectedImage != null) {
                      Preference.setString(
                          Preference.PROFILE_IMAGE, '$selectedImage');
                      _updateUserProfileImage(selectedImage);
                    }
                    setState(() {});
                  });
                  Navigator.pop(context);
                },
              ),
              Container(
                height: 0.5,
                color: Colors.grey[100],
              ),
              ListTile(
                leading: Icon(
                  Icons.camera_alt_outlined,
                  color: context.appColors.primaryForeground,
                ),
                title: Text(
                  'camera',
                  style: TextStyle(color: context.appColors.primaryForeground),
                ).tr(),
                onTap: () async {
                  final cameras = await availableCameras();

                  // Get a specific camera from the list of available cameras.
                  final firstCamera = cameras.first;

                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => TakePictureScreen(
                                camera: firstCamera,
                                cameras: cameras,
                              ))).then((value) async {
                    selectedImage = value;
                    selectedImage = await _cropImage(value);
                    _updateUserProfileImage(selectedImage);
                    setState(() {});
                  });
                  Navigator.pop(context);
                },
              ),
            ],
          );
        });
  }
}

class BlankPage extends StatelessWidget {
  const BlankPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Container(
        //   height: 100,
        //   child: HeaderWidget(100, false, Icons.house_rounded),
        // ),
        Container(
          alignment: Alignment.center,
          margin: EdgeInsets.fromLTRB(0, 10, 0, 10),
          padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
          child: Column(
            children: [
              Container(
                height: 100.0,
                width: 100.0,
                padding: EdgeInsets.all(2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  border: Border.all(width: 0, color: Colors.transparent),
                  color: context.appColors.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 100,
                      offset: const Offset(5, 5),
                    ),
                  ],
                ),
                child: Center(
                  child: ClipOval(
                    child: SvgPicture.asset(
                      'assets/images/bxs_user_circle.svg',
                      allowDrawingOutsideViewBox: true,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Shimmer.fromColors(
                baseColor: context.appColors.shimmerBase,
                highlightColor: context.appColors.shimmerHighlight,
                enabled: true,
                child: Container(
                    height: 12, width: 200, color: context.appColors.grey),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: context.appColors.shimmerBase,
                highlightColor: context.appColors.shimmerHighlight,
                enabled: true,
                child: Container(
                    height: 12, width: 150, color: context.appColors.grey),
              ),
              SizedBox(
                height: 10,
              ),
              Container(
                padding: EdgeInsets.all(10),
                child: Column(
                  children: <Widget>[
                    Card(
                      child: Container(
                        alignment: Alignment.topLeft,
                        padding: EdgeInsets.all(15),
                        child: Column(
                          children: <Widget>[
                            Column(
                              children: <Widget>[
                                Shimmer.fromColors(
                                  baseColor: context.appColors.shimmerBase,
                                  highlightColor:
                                      context.appColors.shimmerHighlight,
                                  enabled: true,
                                  child: ListTile(
                                    leading: Icon(Icons.email),
                                    title: Text("Email",
                                        style: TextStyle(fontSize: 15)),
                                  ),
                                ),
                                Shimmer.fromColors(
                                  baseColor: context.appColors.shimmerBase,
                                  highlightColor:
                                      context.appColors.shimmerHighlight,
                                  enabled: true,
                                  child: ListTile(
                                    leading: Icon(Icons.phone),
                                    title: Text("Phone",
                                        style: TextStyle(fontSize: 15)),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        )
      ],
    );
  }
}
