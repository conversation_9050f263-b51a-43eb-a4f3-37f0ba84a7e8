import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import 'notification_route.dart';
import 'package:flutter/foundation.dart';

Map<String, dynamic>? currentPayload;

class NotificationService {
  final FlutterLocalNotificationsPlugin notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initNotification() async {
    try {
      AndroidInitializationSettings initializationSettingsAndroid =
          const AndroidInitializationSettings('@mipmap/ic_launcher_mec');

      var initializationSettingsIOS = DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true);

      var initializationSettings = InitializationSettings(
          android: initializationSettingsAndroid, iOS: initializationSettingsIOS);
      
      await notificationsPlugin.initialize(initializationSettings,
          onDidReceiveNotificationResponse:
              (NotificationResponse notificationResponse) async {
        if (currentPayload != null) {
          NotificationRoute().open(currentPayload!);
        }
        // try {
        //   Provider.of<MenuListProvider>(context, listen: false)
        //       .updateCurrentIndex(currentPayload?['route']);
        //   Navigator.of(context).popUntil((route) => route.isFirst);
        // } catch (e) {
        //  debugPrint('exception on routing: $e');
        // }
      });
    } catch (e) {
      debugPrint('Error initializing notifications: $e');
      // Continue app execution even if notifications fail to initialize
    }
  }

  NotificationDetails notificationDetails() {
    return const NotificationDetails(
        android: AndroidNotificationDetails('channelId', 'channelName',
            importance: Importance.max),
        iOS: DarwinNotificationDetails());
  }

  Future showNotification(
      {int id = 0,
      String? title,
      String? body,
      Map<String, dynamic>? payLoad}) async {
    currentPayload = payLoad;
    debugPrint("route is $currentPayload and $payLoad");
    return notificationsPlugin.show(id, title, body, notificationDetails());
  }
}
