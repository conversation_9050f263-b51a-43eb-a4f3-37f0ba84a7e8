import 'dart:io';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class AddCertificate extends StatefulWidget {
  final bool? isEditMode;
  final CommonProfession? cetificate;
  const AddCertificate({super.key, this.isEditMode = false, this.cetificate});

  @override
  State<AddCertificate> createState() => _AddCertificateState();
}

class _AddCertificateState extends State<AddCertificate> {
  TextEditingController titleController = TextEditingController();
  TextEditingController startDate = TextEditingController();
  DateTime selectedDate = DateTime.now();

  File? uploadCerti;
  bool? isAddCertificateLoading = false;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    updateData();
    super.initState();
  }

  void updateData() {
    if (widget.isEditMode == true) {
      titleController =
          TextEditingController(text: '${widget.cetificate?.title}');
      startDate =
          TextEditingController(text: '${widget.cetificate?.startDate}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (value) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is AddActivitiesState) handleAddCertificate(state);
            },
            child: Scaffold(
                backgroundColor: context.appColors.background,
                appBar: AppBar(
                  backgroundColor: context.appColors.surface,
                  elevation: 0,
                  leading: SizedBox(),
                  centerTitle: true,
                  title: Text(
                    widget.isEditMode == true
                        ? 'edit_certificate'
                        : 'add_certificate',
                    style: Styles.bold(
                        size: 14, color: context.appColors.headingPrimaryColor),
                  ).tr(),
                  actions: [
                    IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(Icons.close_outlined,
                            color: context.appColors.headingPrimaryColor)),
                  ],
                ),
                body: ScreenWithLoader(
                  isLoading: isAddCertificateLoading,
                  body: Padding(
                    padding: const EdgeInsets.only(top: 0.0),
                    child: SizedBox(
                      height: height(context) * 0.6,
                      child: SingleChildScrollView(
                        child: Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: SingleChildScrollView(
                                      child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                        Text(
                                          "${tr('certificate_title')}*",
                                          style: Styles.regular(
                                              size: 14,
                                              color: context.appColors
                                                  .headingPrimaryColor),
                                        ),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        CustomTextField(
                                            validate: true,
                                            validationString:
                                                tr('please_enter_title'),
                                            controller: titleController,
                                            maxChar: 60,
                                            hintText:
                                                tr('project_placeholder')),
                                        const SizedBox(
                                          height: 20,
                                        ),
                                        Text(
                                          "${tr('Receiving_date')}*",
                                          style: Styles.regular(
                                              size: 14,
                                              color: context.appColors
                                                  .headingPrimaryColor),
                                        ),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        InkWell(
                                          onTap: () {
                                            try {
                                              selectDate(context, startDate);
                                            } catch (e) {
                                              startDate =
                                                  TextEditingController();
                                              selectDate(context, startDate);
                                            }
                                          },
                                          child: Container(
                                            width: width(context),
                                            height: height(context) * 0.072,
                                            decoration: BoxDecoration(
                                              color: context.appColors.surface,
                                              border: Border.all(
                                                  width: 1.0,
                                                  color: const Color.fromARGB(
                                                      255, 142, 142, 142)),
                                              borderRadius:
                                                  const BorderRadius.all(
                                                      Radius.circular(10.0)),
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Text(
                                                    startDate.value.text != ""
                                                        ? startDate.value.text
                                                        : tr('select_date'),
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: startDate.value
                                                                    .text !=
                                                                ""
                                                            ? context.appColors
                                                                .textBlack
                                                            : Color(
                                                                0xff929BA3)),
                                                  ),
                                                ),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 8.0,
                                                          left: 8.0),
                                                  child: SvgPicture.asset(
                                                      'assets/images/selected_calender.svg'),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 60,
                                        ),
                                        CustomUpload(
                                            onClick: () async {
                                              // final picker = ImagePicker();
                                              final pickedFileC =
                                                  await ImagePicker().pickImage(
                                                source: ImageSource.gallery,
                                                imageQuality: 100,
                                              );
                                              if (pickedFileC != null) {
                                                setState(() {
                                                  uploadCerti =
                                                      File(pickedFileC.path);
                                                });
                                              } else if (Platform.isAndroid) {
                                                // No additional action required for lost data in this context.
                                              }
                                            },
                                            uploadText:
                                                '${tr('upload_certificate')}*'),
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Center(
                                            child: Text(
                                                uploadCerti != null
                                                    ? '${uploadCerti?.path.split('/').last}'
                                                    : widget.cetificate
                                                            ?.imageName ??
                                                        tr('upload_only_image'),
                                                style: TextStyle(
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w400,
                                                    color: context
                                                        .appColors.grey3)),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 20,
                                        ),
                                        PortfolioCustomButton(
                                          clickAction: () async {
                                            if (_formKey.currentState!
                                                .validate()) {
                                              if (startDate.value.text == '') {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(tr(
                                                          'please_choose_start_date'))),
                                                );
                                                return;
                                              }
                                              Map<String, dynamic> data = {};
                                              setState(() {
                                                isAddCertificateLoading = true;
                                              });
                                              try {
                                                data["activity_type"] =
                                                    "Certificate";
                                                if (widget.isEditMode == true) {
                                                  if (uploadCerti?.path !=
                                                      null) {
                                                    String? fileName =
                                                        uploadCerti?.path
                                                            .split('/')
                                                            .last;

                                                    data['certificate'] =
                                                        await MultipartFile
                                                            .fromFile(
                                                                '${uploadCerti?.path}',
                                                                filename:
                                                                    fileName);
                                                    // await Utility().s3UploadFile('${uploadCerti?.path}').then((value) => data['certificate_cdn']);
                                                  }
                                                } else {
                                                  String? fileName = uploadCerti
                                                      ?.path
                                                      .split('/')
                                                      .last;

                                                  data['certificate'] =
                                                      await MultipartFile.fromFile(
                                                          '${uploadCerti?.path}',
                                                          filename: fileName);
                                                  //  await Utility().s3UploadFile('${uploadCerti?.path}').then((value) => data['certificate_cdn']);
                                                }
                                                data['title'] =
                                                    titleController.value.text;
                                                data['start_date'] =
                                                    startDate.value.text;
                                                data[
                                                    "professional_key"] = widget
                                                            .isEditMode ==
                                                        true
                                                    ? "certificate_${widget.cetificate?.id}"
                                                    : "new_professional";
                                                data["edit_url_professional"] =
                                                    "${widget.cetificate?.imageName}";

                                                addCertificate(data);
                                              } catch (e) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(
                                                              'please_upload_certificate')
                                                          .tr()),
                                                );
                                                setState(() {
                                                  isAddCertificateLoading =
                                                      false;
                                                });
                                              }
                                            }
                                          },
                                        ),
                                      ]))),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ))));
  }

  void addCertificate(Map<String, dynamic> data) {
    BlocProvider.of<HomeBloc>(context).add(AddActivitiesEvent(data: data));
  }

  void handleAddCertificate(AddActivitiesState state) {
    var addCertificateState = state;
    setState(() {
      switch (addCertificateState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add  Certificate....................");
          isAddCertificateLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add  Certificate....................");
          isAddCertificateLoading = false;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(widget.isEditMode == true
                        ? 'certificate_updated_successfully'
                        : 'certificate_added_successfully')
                    .tr()),
          );
          Navigator.pop(context);
          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Certificate....................");
          isAddCertificateLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'add_certificate', parameters: {
            "ERROR": '${addCertificateState.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<void> selectDate(
      BuildContext context, TextEditingController controller,
      {DateTime? startDate}) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: startDate ?? DateTime(1900),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
        controller.text =
            Utility.convertDateFormat(selectedDate, format: 'yyyy-MM-dd');
      });
    }
  }
}
